<?php
require_once 'includes/functions.php';

// Get featured products
$featured_products = get_products(null, 6, true);
$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Griffin Gadgets - Premium Electronics Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="about.php">About</a></li>
                    <li><a href="contact.php">Contact</a></li>
                </ul>
                
                <div class="user-menu">
                    <?php if (is_logged_in()): ?>
                        <a href="cart.php" class="btn btn-info">
                            <i class="fas fa-shopping-cart"></i> Cart (<span id="cart-count"><?= count(get_cart_items()) ?></span>)
                        </a>
                        <div class="dropdown">
                            <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                            <div class="dropdown-content">
                                <?php if ($_SESSION['user_role'] === 'admin'): ?>
                                    <a href="admin/">Admin Panel</a>
                                <?php elseif ($_SESSION['user_role'] === 'sales_rep'): ?>
                                    <a href="sales_rep/">Dashboard</a>
                                <?php else: ?>
                                    <a href="customer/">My Account</a>
                                <?php endif; ?>
                                <a href="logout.php">Logout</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-primary">Login</a>
                        <a href="register.php" class="btn btn-success">Register</a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container text-center">
            <div class="hero-content">
                <h1 class="hero-title">Welcome to Griffin Gadgets</h1>
                <p class="hero-subtitle">Discover the latest in premium electronics and cutting-edge technology. Your trusted partner for innovation and quality.</p>
                <div class="hero-actions">
                    <a href="products.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag"></i> Shop Now
                    </a>
                    <a href="about.php" class="btn btn-light btn-lg">
                        <i class="fas fa-info-circle"></i> Learn More
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="container mt-5">
        <div class="text-center mb-5">
            <h2 class="section-title">Featured Products</h2>
            <p class="section-subtitle">Discover our premium selection of cutting-edge technology</p>
        </div>
        <div class="row g-4">
            <?php
            // Get all featured products dynamically
            $featured_products = get_products(null, null, true);

            foreach ($featured_products as $product):
            ?>
                <div class="col-lg-4 col-md-6">
                    <div class="premium-product-card">
                        <div class="product-image-container">
                            <?php if ($product['image'] && file_exists("images/products/" . $product['image'])): ?>
                                <img src="images/products/<?= htmlspecialchars($product['image']) ?>"
                                     alt="<?= htmlspecialchars($product['name']) ?>"
                                     class="product-image">
                            <?php else: ?>
                                <div class="product-placeholder">
                                    <i class="fas fa-mobile-alt"></i>
                                    <p>Image Coming Soon</p>
                                </div>
                            <?php endif; ?>
                            <div class="product-overlay">
                                <a href="product.php?id=<?= $product['id'] ?>" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Quick View
                                </a>
                            </div>
                        </div>
                        <div class="product-content">
                            <div class="product-category">Premium Smartphone</div>
                            <h3 class="product-title"><?= htmlspecialchars($product['name']) ?></h3>
                            <p class="product-description">
                                <?= htmlspecialchars(substr($product['description'], 0, 80)) ?>...
                            </p>
                            <div class="product-price-section">
                                <span class="product-price"><?= format_currency($product['price']) ?></span>
                                <span class="price-label">Starting from</span>
                            </div>
                            <div class="product-actions">
                                <?php if (is_logged_in()): ?>
                                    <button class="btn btn-primary add-to-cart" data-product-id="<?= $product['id'] ?>">
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    </button>
                                    <a href="product.php?id=<?= $product['id'] ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-info-circle"></i> Details
                                    </a>
                                <?php else: ?>
                                    <a href="login.php" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt"></i> Login to Purchase
                                    </a>
                                    <a href="product.php?id=<?= $product['id'] ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-info-circle"></i> Learn More
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-5">
            <a href="products.php" class="btn btn-lg btn-primary">
                <i class="fas fa-th-large"></i> View All Products
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="container mt-3">
        <h2 class="text-center mb-3">Why Choose Griffin Gadgets?</h2>
        <div class="row">
            <div class="col-3">
                <div class="feature-card">
                    <div class="feature-icon shipping">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3>Fast Shipping</h3>
                    <p>Free delivery on orders over ₦100,000. Express shipping available nationwide.</p>
                </div>
            </div>
            <div class="col-3">
                <div class="feature-card">
                    <div class="feature-icon support">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>24/7 Support</h3>
                    <p>Our expert support team is available round the clock to assist you.</p>
                </div>
            </div>
            <div class="col-3">
                <div class="feature-card">
                    <div class="feature-icon warranty">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Warranty</h3>
                    <p>All products come with manufacturer warranty and our quality guarantee.</p>
                </div>
            </div>
            <div class="col-3">
                <div class="feature-card">
                    <div class="feature-icon secure">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h3>Secure Payment</h3>
                    <p>Your transactions are protected with bank-level security encryption.</p>
                </div>
            </div>
        </div>
    </section>



    <!-- Call to Action -->
    <section class="hero mt-3">
        <div class="container text-center">
            <h2>Ready to Upgrade Your Tech?</h2>
            <p>Join thousands of satisfied customers who trust Griffin Gadgets for their electronics needs</p>
            <div class="cta-buttons">
                <?php if (!is_logged_in()): ?>
                    <a href="register.php" class="btn btn-success btn-lg">Create Account</a>
                <?php endif; ?>
                <a href="products.php" class="btn btn-primary btn-lg">Browse Products</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Griffin Gadgets</h3>
                    <p>Your trusted partner for premium electronics and cutting-edge technology.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <a href="products.php">Products</a>
                    <a href="about.php">About Us</a>
                    <a href="contact.php">Contact</a>
                    <a href="terms.php">Terms & Conditions</a>
                    <a href="privacy.php">Privacy Policy</a>
                </div>
                <div class="footer-section">
                    <h3>Categories</h3>
                    <a href="products.php?category=Smartphones">Smartphones</a>
                    <a href="products.php?category=Laptops">Laptops</a>
                    <a href="products.php?category=Tablets">Tablets</a>
                    <a href="products.php?category=Audio">Audio</a>
                    <a href="products.php?category=Wearables">Wearables</a>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-map-marker-alt"></i> 37a St. Michaels Rd, Lagos</p>
                    <p><i class="fas fa-phone"></i> +234-************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Griffin Gadgets. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
