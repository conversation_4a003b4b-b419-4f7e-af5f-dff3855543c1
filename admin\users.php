<?php
require_once '../includes/functions.php';

// Require admin login
require_role('admin');

$user = get_logged_in_user();
$filter = sanitize_input($_GET['filter'] ?? '');

// Handle user approval/actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    if ($action === 'approve' && $user_id > 0) {
        global $pdo;
        $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ? AND role = 'sales_rep'");
        if ($stmt->execute([$user_id])) {
            flash_message('User approved successfully!', 'success');
        } else {
            flash_message('Error approving user.', 'danger');
        }
        redirect('users.php');
    } elseif ($action === 'suspend' && $user_id > 0) {
        global $pdo;
        $stmt = $pdo->prepare("UPDATE users SET status = 'suspended' WHERE id = ? AND id != ?");
        if ($stmt->execute([$user_id, $_SESSION['user_id']])) {
            flash_message('User suspended successfully!', 'warning');
        } else {
            flash_message('Error suspending user.', 'danger');
        }
        redirect('users.php');
    }
}

// Get users based on filter
global $pdo;
$sql = "SELECT * FROM users WHERE 1=1";
$params = [];

if ($filter === 'pending') {
    $sql .= " AND status = 'pending'";
} elseif ($filter === 'sales_rep') {
    $sql .= " AND role = 'sales_rep'";
} elseif ($filter === 'customer') {
    $sql .= " AND role = 'customer'";
}

$sql .= " ORDER BY created_at DESC";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll();

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Griffin Gadgets Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="users.php" class="active">Users</a></li>
                    <li><a href="orders.php">Orders</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Admin: <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Page Header -->
        <div class="card mb-3">
            <div class="card-body">
                <h1><i class="fas fa-users"></i> User Management</h1>
                <p class="text-muted">Manage customer accounts and approve sales representatives.</p>
            </div>
        </div>

        <!-- Filter Buttons -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="btn-group" role="group">
                    <a href="users.php" class="btn <?= empty($filter) ? 'btn-primary' : 'btn-outline-primary' ?>">
                        <i class="fas fa-users"></i> All Users
                    </a>
                    <a href="users.php?filter=pending" class="btn <?= $filter === 'pending' ? 'btn-warning' : 'btn-outline-warning' ?>">
                        <i class="fas fa-clock"></i> Pending Approval
                    </a>
                    <a href="users.php?filter=customer" class="btn <?= $filter === 'customer' ? 'btn-success' : 'btn-outline-success' ?>">
                        <i class="fas fa-user"></i> Customers
                    </a>
                    <a href="users.php?filter=sales_rep" class="btn <?= $filter === 'sales_rep' ? 'btn-info' : 'btn-outline-info' ?>">
                        <i class="fas fa-briefcase"></i> Sales Reps
                    </a>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-list"></i> 
                    <?php if ($filter === 'pending'): ?>
                        Pending Users
                    <?php elseif ($filter === 'customer'): ?>
                        Customer Accounts
                    <?php elseif ($filter === 'sales_rep'): ?>
                        Sales Representatives
                    <?php else: ?>
                        All Users
                    <?php endif; ?>
                    (<?= count($users) ?> found)
                </h3>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-users" style="font-size: 4rem; color: #ccc; margin-bottom: 2rem;"></i>
                        <h4>No users found</h4>
                        <p class="text-muted">
                            <?php if ($filter === 'pending'): ?>
                                No users are currently pending approval.
                            <?php else: ?>
                                No users match the current filter.
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $u): ?>
                                    <tr>
                                        <td><?= $u['id'] ?></td>
                                        <td><?= htmlspecialchars($u['full_name'] ?: 'N/A') ?></td>
                                        <td><?= htmlspecialchars($u['username']) ?></td>
                                        <td><?= htmlspecialchars($u['email']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $u['role'] === 'admin' ? 'danger' : ($u['role'] === 'sales_rep' ? 'info' : 'success') ?>">
                                                <?= ucfirst(str_replace('_', ' ', $u['role'])) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $u['status'] === 'active' ? 'success' : ($u['status'] === 'pending' ? 'warning' : 'danger') ?>">
                                                <?= ucfirst($u['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($u['created_at'])) ?></td>
                                        <td>
                                            <?php if ($u['status'] === 'pending' && $u['role'] === 'sales_rep'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="approve">
                                                    <input type="hidden" name="user_id" value="<?= $u['id'] ?>">
                                                    <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Approve this sales representative?')">
                                                        <i class="fas fa-check"></i> Approve
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            
                                            <?php if ($u['id'] != $_SESSION['user_id'] && $u['status'] === 'active'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="suspend">
                                                    <input type="hidden" name="user_id" value="<?= $u['id'] ?>">
                                                    <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('Suspend this user?')">
                                                        <i class="fas fa-ban"></i> Suspend
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            
                                            <a href="user-details.php?id=<?= $u['id'] ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-3">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
