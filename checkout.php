<?php
require_once 'includes/functions.php';
require_once 'includes/receipt_functions.php';

// Require login
require_login();

$cart_items = get_cart_items();
$cart_total = get_cart_total();

// Redirect if cart is empty
if (empty($cart_items)) {
    flash_message('Your cart is empty!', 'warning');
    redirect('cart.php');
}

$user = get_logged_in_user();
$shipping_cost = $cart_total >= 100000 ? 0 : 5000;
$total_amount = $cart_total + $shipping_cost;

$error = '';
$success = '';
$receipt_code = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $shipping_address = sanitize_input($_POST['shipping_address'] ?? '');
    $payment_method = sanitize_input($_POST['payment_method'] ?? 'paystack');

    if (empty($shipping_address)) {
        $error = 'Please provide a shipping address';
    } else {
        // Create order
        $order_id = create_order($payment_method);

        if ($order_id) {
            // Update order with shipping address
            global $pdo;
            $stmt = $pdo->prepare("UPDATE orders SET shipping_address = ? WHERE id = ?");
            $stmt->execute([$shipping_address, $order_id]);

            // Generate receipt for all payment methods
            $receipt_result = create_receipt($order_id, $_SESSION['user_id'], $cart_items, $total_amount, $payment_method);

            if ($receipt_result['success']) {
                $receipt_code = $receipt_result['receipt_code'];

                if ($payment_method === 'paystack') {
                    // Store receipt code in session for after payment
                    $_SESSION['receipt_code'] = $receipt_code;
                    $_SESSION['pending_order_id'] = $order_id;

                    $order = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
                    $order->execute([$order_id]);
                    $order_data = $order->fetch();

                    redirect('payment.php?order=' . $order_data['order_number']);
                } else {
                    // For other payment methods, show success with receipt
                    clear_cart();
                    $success = 'Order placed successfully! Your receipt has been generated.';
                }
            } else {
                $error = 'Error generating receipt. Please try again.';
            }
        } else {
            $error = 'Error creating order. Please try again.';
        }
    }
}

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="cart.php">Cart</a></li>
                    <li><a href="#" class="active">Checkout</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                    <a href="logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <?php if ($success && $receipt_code): ?>
            <!-- Success Message with Receipt -->
            <div class="alert alert-success">
                <?= htmlspecialchars($success) ?>
            </div>

            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-receipt"></i> Payment Successful!</h3>
                </div>
                <div class="card-body text-center">
                    <div class="receipt-code-display">
                        <h4>Your Pickup Code:</h4>
                        <div class="pickup-code"><?= htmlspecialchars($receipt_code) ?></div>
                        <p class="text-muted">Present this code when collecting your items</p>
                    </div>

                    <div class="mt-4">
                        <a href="receipt.php?code=<?= urlencode($receipt_code) ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-print"></i> View & Print Receipt
                        </a>
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-shopping-bag"></i> Continue Shopping
                        </a>
                        <a href="customer/" class="btn btn-info">
                            <i class="fas fa-user"></i> My Account
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Checkout Progress -->
            <div class="checkout-progress mb-3">
                <div class="progress-step completed">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Cart</span>
                </div>
                <div class="progress-step active">
                    <i class="fas fa-credit-card"></i>
                    <span>Checkout</span>
                </div>
                <div class="progress-step">
                    <i class="fas fa-check-circle"></i>
                    <span>Complete</span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!$success): ?>
        <div class="row">
            <div class="col-8">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-credit-card"></i> Checkout</h2>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <!-- Customer Information -->
                            <div class="checkout-section">
                                <h3><i class="fas fa-user"></i> Customer Information</h3>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" 
                                                   value="<?= htmlspecialchars($user['full_name']) ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" 
                                                   value="<?= htmlspecialchars($user['email']) ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">Phone</label>
                                            <input type="tel" class="form-control" 
                                                   value="<?= htmlspecialchars($user['phone']) ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Shipping Address -->
                            <div class="checkout-section">
                                <h3><i class="fas fa-truck"></i> Shipping Address</h3>
                                <div class="form-group">
                                    <label for="shipping_address" class="form-label">Complete Address *</label>
                                    <textarea id="shipping_address" name="shipping_address" class="form-control" 
                                              rows="3" required placeholder="Enter your complete shipping address including street, city, state, and postal code"><?= htmlspecialchars($_POST['shipping_address'] ?? '') ?></textarea>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="checkout-section">
                                <h3><i class="fas fa-credit-card"></i> Payment Method</h3>
                                <div class="payment-methods">
                                    <div class="payment-option">
                                        <input type="radio" id="paystack" name="payment_method" value="paystack" checked>
                                        <label for="paystack" class="payment-label">
                                            <div class="payment-info">
                                                <strong>Paystack</strong>
                                                <p>Pay securely with your debit/credit card</p>
                                            </div>
                                            <div class="payment-logos">
                                                <i class="fab fa-cc-visa"></i>
                                                <i class="fab fa-cc-mastercard"></i>
                                                <i class="fas fa-university"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="checkout-actions">
                                <a href="cart.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Cart
                                </a>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-lock"></i> Place Order - <?= format_currency($total_amount) ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-4">
                <!-- Order Summary -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-receipt"></i> Order Summary</h3>
                    </div>
                    <div class="card-body">
                        <!-- Order Items -->
                        <div class="order-items">
                            <?php foreach ($cart_items as $item): ?>
                                <div class="order-item">
                                    <div class="item-info">
                                        <h5><?= htmlspecialchars($item['name']) ?></h5>
                                        <p>Qty: <?= $item['quantity'] ?> × <?= format_currency($item['price']) ?></p>
                                    </div>
                                    <div class="item-total">
                                        <?= format_currency($item['subtotal']) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <hr>

                        <!-- Totals -->
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span><?= format_currency($cart_total) ?></span>
                        </div>
                        <div class="summary-row">
                            <span>Shipping:</span>
                            <span><?= $shipping_cost > 0 ? format_currency($shipping_cost) : 'FREE' ?></span>
                        </div>
                        <hr>
                        <div class="summary-row total">
                            <span><strong>Total:</strong></span>
                            <strong><?= format_currency($total_amount) ?></strong>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt" style="font-size: 2rem; color: #28a745; margin-bottom: 1rem;"></i>
                        <h5>Secure Checkout</h5>
                        <p class="text-muted small">Your payment information is protected with 256-bit SSL encryption.</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="js/main.js"></script>
    <style>
        .checkout-progress {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            margin: 0 2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .progress-step i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .progress-step.completed {
            background: #28a745;
            color: white;
        }
        .progress-step.active {
            background: #667eea;
            color: white;
        }
        .progress-step:not(.completed):not(.active) {
            background: #f8f9fa;
            color: #6c757d;
        }
        .checkout-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #dee2e6;
        }
        .checkout-section:last-child {
            border-bottom: none;
        }
        .checkout-section h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        .payment-methods {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .payment-option {
            position: relative;
        }
        .payment-option input[type="radio"] {
            display: none;
        }
        .payment-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-option input[type="radio"]:checked + .payment-label {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .payment-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        .payment-logos {
            display: flex;
            gap: 0.5rem;
            font-size: 1.5rem;
            color: #666;
        }
        .checkout-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
        }
        .order-items {
            margin-bottom: 1rem;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-info h5 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
        }
        .item-info p {
            margin: 0;
            color: #666;
            font-size: 0.875rem;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .summary-row.total {
            font-size: 1.2rem;
            margin-top: 1rem;
        }
        .receipt-code-display {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
        }
        .pickup-code {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 2px dashed #667eea;
            margin: 1rem 0;
            letter-spacing: 2px;
        }
    </style>
</body>
</html>
