<?php
// Receipt Generation Functions for Griffin Gadgets

/**
 * Generate a unique receipt code
 */
function generate_receipt_code() {
    return 'GG-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

/**
 * Create a receipt record in the database
 */
function create_receipt($order_id, $user_id, $items, $total_amount, $payment_method = 'cash') {
    global $pdo;
    
    $receipt_code = generate_receipt_code();
    
    try {
        $pdo->beginTransaction();
        
        // Insert receipt record
        $stmt = $pdo->prepare("
            INSERT INTO receipts (receipt_code, order_id, user_id, total_amount, payment_method, items_json, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $items_json = json_encode($items);
        $stmt->execute([$receipt_code, $order_id, $user_id, $total_amount, $payment_method, $items_json]);
        
        $receipt_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        return [
            'receipt_id' => $receipt_id,
            'receipt_code' => $receipt_code,
            'success' => true
        ];
        
    } catch (Exception $e) {
        $pdo->rollback();
        error_log("Receipt creation error: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get receipt by code
 */
function get_receipt_by_code($receipt_code) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT r.*, u.full_name, u.email, u.phone 
        FROM receipts r 
        JOIN users u ON r.user_id = u.id 
        WHERE r.receipt_code = ?
    ");
    $stmt->execute([$receipt_code]);
    
    $receipt = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($receipt) {
        $receipt['items'] = json_decode($receipt['items_json'], true);
    }
    
    return $receipt;
}

/**
 * Get all receipts for a user
 */
function get_user_receipts($user_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT * FROM receipts 
        WHERE user_id = ? 
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user_id]);
    
    $receipts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($receipts as &$receipt) {
        $receipt['items'] = json_decode($receipt['items_json'], true);
    }
    
    return $receipts;
}

/**
 * Generate HTML receipt for display/printing
 */
function generate_receipt_html($receipt_code) {
    $receipt = get_receipt_by_code($receipt_code);
    
    if (!$receipt) {
        return false;
    }
    
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Receipt - ' . htmlspecialchars($receipt_code) . '</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 0 auto; padding: 20px; }
            .receipt-header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
            .receipt-header h1 { margin: 0; color: #667eea; }
            .receipt-info { margin-bottom: 20px; }
            .receipt-info div { margin-bottom: 5px; }
            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .items-table th, .items-table td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
            .items-table th { background: #f5f5f5; font-weight: bold; }
            .total-section { border-top: 2px solid #333; padding-top: 10px; }
            .total-row { display: flex; justify-content: space-between; margin-bottom: 5px; }
            .total-final { font-weight: bold; font-size: 1.2em; }
            .receipt-footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; }
            .receipt-code { background: #f0f0f0; padding: 10px; text-align: center; font-weight: bold; margin: 20px 0; }
            @media print {
                body { margin: 0; padding: 10px; }
                .no-print { display: none; }
            }
        </style>
    </head>
    <body>
        <div class="receipt-header">
            <h1>⚡ Griffin Gadgets</h1>
            <p>37a St. Michaels Rd<br>+234-************</p>
        </div>
        
        <div class="receipt-info">
            <div><strong>Receipt #:</strong> ' . htmlspecialchars($receipt['receipt_code']) . '</div>
            <div><strong>Date:</strong> ' . date('M d, Y H:i', strtotime($receipt['created_at'])) . '</div>
            <div><strong>Customer:</strong> ' . htmlspecialchars($receipt['full_name']) . '</div>
            <div><strong>Email:</strong> ' . htmlspecialchars($receipt['email']) . '</div>
            ' . ($receipt['phone'] ? '<div><strong>Phone:</strong> +234' . htmlspecialchars($receipt['phone']) . '</div>' : '') . '
            <div><strong>Payment:</strong> ' . ucfirst(htmlspecialchars($receipt['payment_method'])) . '</div>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Qty</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($receipt['items'] as $item) {
        $html .= '
                <tr>
                    <td>' . htmlspecialchars($item['name']) . '</td>
                    <td>' . $item['quantity'] . '</td>
                    <td>' . format_currency($item['price']) . '</td>
                    <td>' . format_currency($item['subtotal']) . '</td>
                </tr>';
    }
    
    $html .= '
            </tbody>
        </table>
        
        <div class="total-section">
            <div class="total-row total-final">
                <span>TOTAL PAID:</span>
                <span>' . format_currency($receipt['total_amount']) . '</span>
            </div>
        </div>
        
        <div class="receipt-code">
            <div>Pickup Code</div>
            <div style="font-size: 1.5em; color: #667eea;">' . htmlspecialchars($receipt['receipt_code']) . '</div>
            <div style="font-size: 0.9em; margin-top: 5px;">Present this code when collecting your items</div>
        </div>
        
        <div class="receipt-footer">
            <p><strong>Thank you for shopping with Griffin Gadgets!</strong></p>
            <p style="font-size: 0.9em;">Keep this receipt for your records and item pickup</p>
            <div class="no-print" style="margin-top: 20px;">
                <button onclick="window.print()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">Print Receipt</button>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

/**
 * Create receipts table if it doesn't exist
 */
function create_receipts_table() {
    global $pdo;
    
    $sql = "
    CREATE TABLE IF NOT EXISTS receipts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        receipt_code VARCHAR(20) UNIQUE NOT NULL,
        order_id INT NULL,
        user_id INT NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) DEFAULT 'cash',
        items_json TEXT NOT NULL,
        status ENUM('pending', 'collected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        collected_at TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $pdo->exec($sql);
}

// Initialize receipts table
create_receipts_table();
?>
