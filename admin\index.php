<?php
require_once '../includes/functions.php';

// Require admin login
require_role('admin');

$user = get_logged_in_user();

// Get dashboard statistics
global $pdo;

// Total users
$stmt = $pdo->query("SELECT COUNT(*) FROM users");
$total_users = $stmt->fetchColumn();

// Pending sales reps
$stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'sales_rep' AND status = 'pending'");
$pending_sales_reps = $stmt->fetchColumn();

// Total products
$stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
$total_products = $stmt->fetchColumn();

// Total orders
$stmt = $pdo->query("SELECT COUNT(*) FROM orders");
$total_orders = $stmt->fetchColumn();

// Total revenue
$stmt = $pdo->query("SELECT SUM(total_amount) FROM orders WHERE status != 'cancelled'");
$total_revenue = $stmt->fetchColumn() ?: 0;

// Recent orders
$stmt = $pdo->prepare("
    SELECT o.*, u.full_name, u.username 
    FROM orders o 
    JOIN users u ON o.user_id = u.id 
    ORDER BY o.created_at DESC 
    LIMIT 5
");
$stmt->execute();
$recent_orders = $stmt->fetchAll();

// Low stock products
$stmt = $pdo->query("SELECT * FROM products WHERE stock_quantity <= 10 AND status = 'active' ORDER BY stock_quantity ASC LIMIT 5");
$low_stock_products = $stmt->fetchAll();

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Griffin Gadgets</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php" class="active">Dashboard</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="users.php">Users</a></li>
                    <li><a href="orders.php">Orders</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Admin: <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Welcome Section -->
        <div class="card mb-3">
            <div class="card-body">
                <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
                <p class="text-muted">Welcome back, <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?>! Here's an overview of your Griffin Gadgets store.</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-3">
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="feature-icon support mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3><?= number_format($total_users) ?></h3>
                        <p>Total Users</p>
                        <?php if ($pending_sales_reps > 0): ?>
                            <small class="text-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <?= $pending_sales_reps ?> pending approval
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="feature-icon shipping mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                            <i class="fas fa-cube"></i>
                        </div>
                        <h3><?= number_format($total_products) ?></h3>
                        <p>Active Products</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="feature-icon warranty mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <h3><?= number_format($total_orders) ?></h3>
                        <p>Total Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="feature-icon secure mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                            <i class="fas fa-naira-sign"></i>
                        </div>
                        <h3><?= format_currency($total_revenue) ?></h3>
                        <p>Total Revenue</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-3">
                        <a href="products.php?action=add" class="quick-action">
                            <i class="fas fa-plus-circle"></i>
                            <span>Add Product</span>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="users.php?filter=pending" class="quick-action">
                            <i class="fas fa-user-check"></i>
                            <span>Approve Users</span>
                            <?php if ($pending_sales_reps > 0): ?>
                                <span class="badge"><?= $pending_sales_reps ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="orders.php" class="quick-action">
                            <i class="fas fa-list-alt"></i>
                            <span>View Orders</span>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="../reset-system.php" class="quick-action danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>System Reset</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Orders -->
            <div class="col-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Recent Orders</h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_orders)): ?>
                            <div class="text-center p-3">
                                <i class="fas fa-shopping-bag" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                                <h4>No orders yet</h4>
                                <p class="text-muted">Orders will appear here once customers start purchasing.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($order['order_number']) ?></td>
                                                <td><?= htmlspecialchars($order['full_name'] ?: $order['username']) ?></td>
                                                <td><?= date('M j, Y', strtotime($order['created_at'])) ?></td>
                                                <td><?= format_currency($order['total_amount']) ?></td>
                                                <td>
                                                    <span class="badge badge-<?= $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'cancelled' ? 'danger' : 'warning') ?>">
                                                        <?= ucfirst($order['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="order-details.php?id=<?= $order['id'] ?>" class="btn btn-sm btn-info">
                                                        View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="orders.php" class="btn btn-primary">View All Orders</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Alerts & Notifications -->
            <div class="col-4">
                <!-- Low Stock Alert -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> Low Stock Alert</h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($low_stock_products)): ?>
                            <div class="text-center">
                                <i class="fas fa-check-circle" style="font-size: 2rem; color: #28a745; margin-bottom: 1rem;"></i>
                                <p class="text-success">All products are well stocked!</p>
                            </div>
                        <?php else: ?>
                            <div class="low-stock-list">
                                <?php foreach ($low_stock_products as $product): ?>
                                    <div class="low-stock-item">
                                        <div class="product-info">
                                            <h5><?= htmlspecialchars($product['name']) ?></h5>
                                            <p class="text-danger">Only <?= $product['stock_quantity'] ?> left</p>
                                        </div>
                                        <a href="products.php?action=edit&id=<?= $product['id'] ?>" class="btn btn-sm btn-warning">
                                            Update
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="products.php?filter=low_stock" class="btn btn-warning">View All</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- System Info -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> System Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="system-info">
                            <div class="info-item">
                                <span>PHP Version:</span>
                                <strong><?= PHP_VERSION ?></strong>
                            </div>
                            <div class="info-item">
                                <span>Database:</span>
                                <strong>MySQL/MariaDB</strong>
                            </div>
                            <div class="info-item">
                                <span>Last Login:</span>
                                <strong><?= date('M j, Y g:i A') ?></strong>
                            </div>
                        </div>
                        
                        <div class="system-actions mt-3">
                            <a href="../importdb.php" class="btn btn-sm btn-info">Database Setup</a>
                            <a href="../direct-reset.php" class="btn btn-sm btn-warning">Reset Password</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <style>
        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem 1rem;
            text-decoration: none;
            color: #333;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }
        .quick-action:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
        }
        .quick-action.danger:hover {
            border-color: #dc3545;
            color: #dc3545;
        }
        .quick-action i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .quick-action .badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }
        .low-stock-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .low-stock-item:last-child {
            border-bottom: none;
        }
        .product-info h5 {
            margin: 0 0 0.25rem 0;
            font-size: 0.9rem;
        }
        .product-info p {
            margin: 0;
            font-size: 0.8rem;
        }
        .system-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .system-actions {
            display: flex;
            gap: 0.5rem;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .badge-success { background: #28a745; color: white; }
        .badge-warning { background: #ffc107; color: #212529; }
        .badge-danger { background: #dc3545; color: white; }
    </style>
</body>
</html>
