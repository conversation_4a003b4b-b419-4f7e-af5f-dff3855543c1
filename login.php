<?php
require_once 'includes/functions.php';

// Redirect if already logged in
if (is_logged_in()) {
    $role = $_SESSION['user_role'];
    switch ($role) {
        case 'admin':
            redirect('admin/');
            break;
        case 'sales_rep':
            redirect('sales_rep/');
            break;
        default:
            redirect('customer/');
            break;
    }
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        if (authenticate_user($username, $password)) {
            $role = $_SESSION['user_role'];
            flash_message('Welcome back!', 'success');
            
            // Redirect based on role
            switch ($role) {
                case 'admin':
                    redirect('admin/');
                    break;
                case 'sales_rep':
                    redirect('sales_rep/');
                    break;
                default:
                    redirect('customer/');
                    break;
            }
        } else {
            $error = 'Invalid username or password';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .auth-card {
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .auth-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .auth-header p {
            color: #666;
        }
        .auth-links {
            text-align: center;
            margin-top: 2rem;
        }
        .auth-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
        }
        .auth-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1><i class="fas fa-bolt"></i> Griffin Gadgets</h1>
                <p>Sign in to your account</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <form method="POST" data-validate>
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i> Username or Email
                    </label>
                    <input type="text" id="username" name="username" class="form-control" 
                           value="<?= htmlspecialchars($username ?? '') ?>" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <input type="password" id="password" name="password" class="form-control" required>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-sign-in-alt"></i> Sign In
                    </button>
                </div>
            </form>

            <div class="auth-links">
                <a href="register.php">Create Account</a>
                <a href="forgot-password.php">Forgot Password?</a>
            </div>

            <div class="auth-links mt-3">
                <a href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Home
                </a>
            </div>

            <!-- Demo Credentials -->
            <div class="card mt-3" style="background-color: #f8f9fa;">
                <div class="card-body">
                    <h6 style="margin-bottom: 1rem; color: #666;">Demo Credentials:</h6>
                    <small style="display: block; margin-bottom: 0.5rem;">
                        <strong>Admin:</strong> admin / admin123
                    </small>
                    <small style="display: block; margin-bottom: 0.5rem;">
                        <strong>Customer:</strong> Register as customer for immediate access
                    </small>
                    <small style="display: block;">
                        <strong>Sales Rep:</strong> Register as sales rep (requires admin approval)
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
