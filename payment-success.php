<?php
require_once 'includes/functions.php';

// Require login
require_login();

$reference = sanitize_input($_GET['reference'] ?? '');
$order_number = sanitize_input($_GET['order'] ?? '');

if (empty($reference) || empty($order_number)) {
    flash_message('Invalid payment reference', 'danger');
    redirect('cart.php');
}

// Get order details
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM orders WHERE order_number = ? AND user_id = ?");
$stmt->execute([$order_number, $_SESSION['user_id']]);
$order = $stmt->fetch();

if (!$order) {
    flash_message('Order not found', 'danger');
    redirect('cart.php');
}

// Update order status and payment reference
$stmt = $pdo->prepare("UPDATE orders SET status = 'processing', payment_reference = ?, updated_at = NOW() WHERE id = ?");
$stmt->execute([$reference, $order['id']]);

// Get order items
$stmt = $pdo->prepare("
    SELECT oi.*, p.name, p.image 
    FROM order_items oi 
    JOIN products p ON oi.product_id = p.id 
    WHERE oi.order_id = ?
");
$stmt->execute([$order['id']]);
$order_items = $stmt->fetchAll();

$user = get_logged_in_user();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="customer/">My Account</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                    <a href="logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <div class="container mt-3">
        <!-- Success Progress -->
        <div class="checkout-progress mb-3">
            <div class="progress-step completed">
                <i class="fas fa-shopping-cart"></i>
                <span>Cart</span>
            </div>
            <div class="progress-step completed">
                <i class="fas fa-credit-card"></i>
                <span>Checkout</span>
            </div>
            <div class="progress-step completed">
                <i class="fas fa-lock"></i>
                <span>Payment</span>
            </div>
            <div class="progress-step completed">
                <i class="fas fa-check-circle"></i>
                <span>Complete</span>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-8">
                <!-- Success Message -->
                <div class="card success-card">
                    <div class="card-body text-center">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h1>Payment Successful!</h1>
                        <p class="lead">Thank you for your order. Your payment has been processed successfully.</p>
                        
                        <div class="order-info">
                            <div class="info-item">
                                <strong>Order Number:</strong> <?= htmlspecialchars($order['order_number']) ?>
                            </div>
                            <div class="info-item">
                                <strong>Payment Reference:</strong> <?= htmlspecialchars($reference) ?>
                            </div>
                            <div class="info-item">
                                <strong>Amount Paid:</strong> <?= format_currency($order['total_amount']) ?>
                            </div>
                            <div class="info-item">
                                <strong>Order Status:</strong> <span class="badge badge-success">Processing</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Details -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3><i class="fas fa-receipt"></i> Order Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="order-items">
                            <?php foreach ($order_items as $item): ?>
                                <div class="order-item">
                                    <div class="item-image">
                                        <?php if ($item['image'] && file_exists("images/products/" . $item['image'])): ?>
                                            <img src="images/products/<?= htmlspecialchars($item['image']) ?>" 
                                                 alt="<?= htmlspecialchars($item['name']) ?>">
                                        <?php else: ?>
                                            <i class="fas fa-cube"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="item-details">
                                        <h4><?= htmlspecialchars($item['name']) ?></h4>
                                        <p>Quantity: <?= $item['quantity'] ?></p>
                                        <p>Price: <?= format_currency($item['price']) ?> each</p>
                                    </div>
                                    <div class="item-total">
                                        <?= format_currency($item['subtotal']) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="order-total">
                            <div class="total-row">
                                <span>Total Amount:</span>
                                <strong><?= format_currency($order['total_amount']) ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> What's Next?</h3>
                    </div>
                    <div class="card-body">
                        <div class="next-steps">
                            <div class="step">
                                <div class="step-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="step-content">
                                    <h4>Order Confirmation</h4>
                                    <p>You will receive an email confirmation shortly with your order details.</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="step-content">
                                    <h4>Processing</h4>
                                    <p>Your order is being processed and will be prepared for shipping.</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="step-content">
                                    <h4>Shipping</h4>
                                    <p>You'll receive tracking information once your order ships.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="success-actions text-center mt-3">
                    <a href="customer/order-details.php?id=<?= $order['id'] ?>" class="btn btn-primary">
                        <i class="fas fa-eye"></i> View Order Details
                    </a>
                    <a href="products.php" class="btn btn-success">
                        <i class="fas fa-shopping-bag"></i> Continue Shopping
                    </a>
                    <a href="customer/" class="btn btn-info">
                        <i class="fas fa-user"></i> My Account
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .checkout-progress {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            margin: 0 2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .progress-step i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .progress-step.completed {
            background: #28a745;
            color: white;
        }
        .success-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .success-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
            color: white;
        }
        .success-card h1 {
            color: white;
            margin-bottom: 1rem;
        }
        .order-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .badge-success {
            background: #28a745;
            color: white;
        }
        .order-items {
            margin-bottom: 2rem;
        }
        .order-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-image {
            width: 80px;
            height: 80px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            overflow: hidden;
        }
        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .item-image i {
            font-size: 2rem;
            color: #ccc;
        }
        .item-details {
            flex: 1;
        }
        .item-details h4 {
            margin: 0 0 0.5rem 0;
        }
        .item-details p {
            margin: 0.25rem 0;
            color: #666;
        }
        .item-total {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }
        .order-total {
            border-top: 2px solid #dee2e6;
            padding-top: 1rem;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            font-size: 1.3rem;
        }
        .next-steps {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .step {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }
        .step-content h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }
        .step-content p {
            margin: 0;
            color: #666;
        }
        .success-actions {
            margin: 2rem 0;
        }
        .success-actions .btn {
            margin: 0 0.5rem;
        }
        .justify-content-center {
            justify-content: center;
        }
    </style>
</body>
</html>
