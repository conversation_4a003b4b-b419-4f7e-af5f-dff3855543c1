<?php
require_once '../includes/functions.php';
require_once '../includes/receipt_functions.php';

// Require sales rep login
require_login();
if ($_SESSION['user_role'] !== 'sales_rep' && $_SESSION['user_role'] !== 'admin') {
    flash_message('Access denied', 'danger');
    redirect('../index.php');
}

// Get some basic stats
global $pdo;

// Get pending pickups count
$stmt = $pdo->prepare("SELECT COUNT(*) FROM receipts WHERE status = 'pending'");
$stmt->execute();
$pending_pickups = $stmt->fetchColumn();

// Get today's collections
$stmt = $pdo->prepare("SELECT COUNT(*) FROM receipts WHERE status = 'collected' AND DATE(collected_at) = CURDATE()");
$stmt->execute();
$today_collections = $stmt->fetchColumn();

// Get recent receipts
$stmt = $pdo->prepare("
    SELECT r.*, u.full_name 
    FROM receipts r 
    JOIN users u ON r.user_id = u.id 
    ORDER BY r.created_at DESC 
    LIMIT 10
");
$stmt->execute();
$recent_receipts = $stmt->fetchAll(PDO::FETCH_ASSOC);

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Rep Dashboard - Griffin Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <div class="user-menu">
                    <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <div class="container mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-tachometer-alt"></i> Sales Representative Dashboard</h2>
                <p class="text-muted">Manage customer pickups and verify receipt codes</p>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="dashboard-card pending">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="card-content">
                        <h3><?= $pending_pickups ?></h3>
                        <p>Pending Pickups</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="dashboard-card collected">
                    <div class="card-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="card-content">
                        <h3><?= $today_collections ?></h3>
                        <p>Today's Collections</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="dashboard-card verification">
                    <a href="pickup-verification.php" class="card-link">
                        <div class="card-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="card-content">
                            <h3>Verify</h3>
                            <p>Pickup Codes</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Receipts -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-receipt"></i> Recent Receipts</h4>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_receipts)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-receipt" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                                <h5>No receipts found</h5>
                                <p class="text-muted">Recent customer receipts will appear here</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Receipt Code</th>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_receipts as $receipt): ?>
                                            <tr>
                                                <td>
                                                    <code><?= htmlspecialchars($receipt['receipt_code']) ?></code>
                                                </td>
                                                <td><?= htmlspecialchars($receipt['full_name']) ?></td>
                                                <td><?= format_currency($receipt['total_amount']) ?></td>
                                                <td><?= date('M d, Y H:i', strtotime($receipt['created_at'])) ?></td>
                                                <td>
                                                    <span class="badge <?= $receipt['status'] === 'collected' ? 'bg-success' : 'bg-warning' ?>">
                                                        <?= ucfirst($receipt['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="pickup-verification.php" class="btn btn-sm btn-primary" 
                                                       title="Verify this code">
                                                        <i class="fas fa-search"></i>
                                                    </a>
                                                    <a href="../receipt.php?code=<?= urlencode($receipt['receipt_code']) ?>" 
                                                       class="btn btn-sm btn-secondary" target="_blank" title="View receipt">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.25);
        }
        
        .dashboard-card.pending {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .dashboard-card.collected {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .dashboard-card.verification {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .card-link {
            color: inherit;
            text-decoration: none;
            display: block;
        }
        
        .card-link:hover {
            color: inherit;
            text-decoration: none;
        }
        
        .card-icon {
            position: absolute;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.3;
        }
        
        .card-icon i {
            font-size: 3rem;
        }
        
        .card-content h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .card-content p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        code {
            background: #f8f9fa;
            color: #e83e8c;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</body>
</html>
