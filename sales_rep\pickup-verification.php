<?php
require_once '../includes/functions.php';
require_once '../includes/receipt_functions.php';

// Require sales rep login
require_login();
if ($_SESSION['user_role'] !== 'sales_rep' && $_SESSION['user_role'] !== 'admin') {
    flash_message('Access denied', 'danger');
    redirect('../index.php');
}

$receipt = null;
$error = '';
$success = '';

// Handle pickup code verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'verify') {
            $pickup_code = sanitize_input($_POST['pickup_code'] ?? '');
            
            if (empty($pickup_code)) {
                $error = 'Please enter a pickup code';
            } else {
                $receipt = get_receipt_by_code($pickup_code);
                if (!$receipt) {
                    $error = 'Invalid pickup code. Please check and try again.';
                }
            }
        } elseif ($_POST['action'] === 'mark_collected') {
            $receipt_code = sanitize_input($_POST['receipt_code'] ?? '');
            
            if (!empty($receipt_code)) {
                global $pdo;
                $stmt = $pdo->prepare("UPDATE receipts SET status = 'collected', collected_at = NOW() WHERE receipt_code = ?");
                $stmt->execute([$receipt_code]);
                
                $success = 'Order marked as collected successfully!';
                $receipt = get_receipt_by_code($receipt_code);
            }
        }
    }
}

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pickup Verification - Griffin Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <div class="user-menu">
                    <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                    <a href="index.php" class="btn btn-warning">Dashboard</a>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-search"></i> Pickup Code Verification</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($flash): ?>
                            <div class="alert alert-<?= $flash['type'] ?>">
                                <?= htmlspecialchars($flash['message']) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?= htmlspecialchars($success) ?>
                            </div>
                        <?php endif; ?>

                        <!-- Pickup Code Input -->
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="verify">
                            <div class="form-group">
                                <label for="pickup_code" class="form-label">
                                    <i class="fas fa-barcode"></i> Enter Pickup Code
                                </label>
                                <div class="input-group">
                                    <input type="text" id="pickup_code" name="pickup_code" class="form-control form-control-lg" 
                                           placeholder="e.g., GG-20241220-ABC123" 
                                           value="<?= htmlspecialchars($_POST['pickup_code'] ?? '') ?>"
                                           style="text-transform: uppercase;" required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Verify
                                    </button>
                                </div>
                            </div>
                        </form>

                        <?php if ($receipt): ?>
                            <!-- Receipt Details -->
                            <div class="receipt-details">
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> Receipt Found!</h5>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-card">
                                            <h6><i class="fas fa-user"></i> Customer Information</h6>
                                            <p><strong>Name:</strong> <?= htmlspecialchars($receipt['full_name']) ?></p>
                                            <p><strong>Email:</strong> <?= htmlspecialchars($receipt['email']) ?></p>
                                            <?php if ($receipt['phone']): ?>
                                                <p><strong>Phone:</strong> +234<?= htmlspecialchars($receipt['phone']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-card">
                                            <h6><i class="fas fa-receipt"></i> Order Information</h6>
                                            <p><strong>Receipt #:</strong> <?= htmlspecialchars($receipt['receipt_code']) ?></p>
                                            <p><strong>Date:</strong> <?= date('M d, Y H:i', strtotime($receipt['created_at'])) ?></p>
                                            <p><strong>Payment:</strong> <?= ucfirst(htmlspecialchars($receipt['payment_method'])) ?></p>
                                            <p><strong>Status:</strong> 
                                                <span class="badge <?= $receipt['status'] === 'collected' ? 'bg-success' : 'bg-warning' ?>">
                                                    <?= ucfirst($receipt['status']) ?>
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Items List -->
                                <div class="items-list mt-4">
                                    <h6><i class="fas fa-list"></i> Items to Collect</h6>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Item</th>
                                                    <th>Quantity</th>
                                                    <th>Price</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($receipt['items'] as $item): ?>
                                                    <tr>
                                                        <td><?= htmlspecialchars($item['name']) ?></td>
                                                        <td><?= $item['quantity'] ?></td>
                                                        <td><?= format_currency($item['price']) ?></td>
                                                        <td><?= format_currency($item['subtotal']) ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-dark">
                                                    <th colspan="3">Total Amount</th>
                                                    <th><?= format_currency($receipt['total_amount']) ?></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="actions mt-4">
                                    <?php if ($receipt['status'] !== 'collected'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="mark_collected">
                                            <input type="hidden" name="receipt_code" value="<?= htmlspecialchars($receipt['receipt_code']) ?>">
                                            <button type="submit" class="btn btn-success" 
                                                    onclick="return confirm('Mark this order as collected?')">
                                                <i class="fas fa-check"></i> Mark as Collected
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle"></i> This order has already been collected on 
                                            <?= date('M d, Y H:i', strtotime($receipt['collected_at'])) ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <a href="../receipt.php?code=<?= urlencode($receipt['receipt_code']) ?>" 
                                       class="btn btn-primary" target="_blank">
                                        <i class="fas fa-print"></i> View Receipt
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-uppercase pickup code input
        document.getElementById('pickup_code').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
    
    <style>
        .info-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .info-card h6 {
            color: #667eea;
            margin-bottom: 0.75rem;
            font-weight: 600;
        }
        .info-card p {
            margin-bottom: 0.5rem;
        }
        .receipt-details {
            border-top: 2px solid #dee2e6;
            padding-top: 2rem;
        }
        .items-list h6 {
            color: #333;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        .actions {
            border-top: 1px solid #dee2e6;
            padding-top: 1rem;
        }
        .input-group .form-control {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</body>
</html>
