<?php
require_once '../includes/functions.php';

// Require admin login
require_role('admin');

$user = get_logged_in_user();

// Get all products
global $pdo;
$stmt = $pdo->query("SELECT * FROM products ORDER BY created_at DESC");
$products = $stmt->fetchAll();

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - Griffin Gadgets Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="products.php" class="active">Products</a></li>
                    <li><a href="users.php">Users</a></li>
                    <li><a href="orders.php">Orders</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Admin: <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Page Header -->
        <div class="card mb-3">
            <div class="card-body d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-cube"></i> Product Management</h1>
                    <p class="text-muted mb-0">Manage your product catalog and inventory.</p>
                </div>
                <a href="add-product.php" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add New Product
                </a>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-list"></i> All Products (<?= count($products) ?>)</h3>
            </div>
            <div class="card-body">
                <?php if (empty($products)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-cube" style="font-size: 4rem; color: #ccc; margin-bottom: 2rem;"></i>
                        <h4>No products found</h4>
                        <p class="text-muted">Start by adding your first product to the catalog.</p>
                        <a href="add-product.php" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add First Product
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Featured</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <div style="width: 50px; height: 50px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; overflow: hidden;">
                                                <?php if ($product['image'] && file_exists("../images/products/" . $product['image'])): ?>
                                                    <img src="../images/products/<?= htmlspecialchars($product['image']) ?>" 
                                                         alt="<?= htmlspecialchars($product['name']) ?>" 
                                                         style="width: 100%; height: 100%; object-fit: cover;">
                                                <?php else: ?>
                                                    <i class="fas fa-image text-muted"></i>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($product['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= htmlspecialchars(substr($product['description'], 0, 50)) ?>...</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= htmlspecialchars($product['category']) ?></span>
                                        </td>
                                        <td>
                                            <strong><?= format_currency($product['price']) ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $product['stock_quantity'] <= 10 ? 'danger' : ($product['stock_quantity'] <= 20 ? 'warning' : 'success') ?>">
                                                <?= $product['stock_quantity'] ?> units
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $product['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                <?= ucfirst($product['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($product['featured']): ?>
                                                <i class="fas fa-star text-warning" title="Featured"></i>
                                            <?php else: ?>
                                                <i class="far fa-star text-muted" title="Not Featured"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="edit-product.php?id=<?= $product['id'] ?>" class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="../product.php?id=<?= $product['id'] ?>" class="btn btn-outline-info" title="View" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-outline-danger" title="Delete" onclick="deleteProduct(<?= $product['id'] ?>, '<?= htmlspecialchars($product['name']) ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-3">
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success"><?= count(array_filter($products, fn($p) => $p['status'] === 'active')) ?></h4>
                        <p class="mb-0">Active Products</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning"><?= count(array_filter($products, fn($p) => $p['featured'] == 1)) ?></h4>
                        <p class="mb-0">Featured Products</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-danger"><?= count(array_filter($products, fn($p) => $p['stock_quantity'] <= 10)) ?></h4>
                        <p class="mb-0">Low Stock</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info"><?= count(array_unique(array_column($products, 'category'))) ?></h4>
                        <p class="mb-0">Categories</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-3">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/main.js"></script>
    <script>
        function deleteProduct(id, name) {
            if (confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'delete-product.php';
                
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'product_id';
                idInput.value = id;
                
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
