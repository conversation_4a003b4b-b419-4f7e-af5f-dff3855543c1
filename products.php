<?php
require_once 'includes/functions.php';

// Get filter parameters
$category = sanitize_input($_GET['category'] ?? '');
$search = sanitize_input($_GET['search'] ?? '');
$sort = sanitize_input($_GET['sort'] ?? 'newest');

// Get all categories for filter
$categories = get_categories();

// Build query based on filters
global $pdo;
$sql = "SELECT * FROM products WHERE status = 'active'";
$params = [];

if (!empty($category)) {
    $sql .= " AND category = ?";
    $params[] = $category;
}

if (!empty($search)) {
    $sql .= " AND (name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

// Add sorting
switch ($sort) {
    case 'price_low':
        $sql .= " ORDER BY price ASC";
        break;
    case 'price_high':
        $sql .= " ORDER BY price DESC";
        break;
    case 'name':
        $sql .= " ORDER BY name ASC";
        break;
    default:
        $sql .= " ORDER BY created_at DESC";
        break;
}

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$products = $stmt->fetchAll();

// After fetching $products from the database, filter to only the three iPhones and fix image extensions
$products = array_filter($products, function($product) {
    $allowed = [
        'iPhone 13 Pro' => '13pro.JPG',
        'iPhone 14 Pro' => '14pro.JPG',
        'iPhone 15 Pro' => '15pro.jpg',
    ];
    if (isset($allowed[$product['name']])) {
        $product['image'] = $allowed[$product['name']];
        return true;
    }
    return false;
});
$products = array_values($products); // reindex

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Griffin Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css?v=<?= time() ?>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="products.php" class="active">Products</a></li>
                    <li><a href="about.php">About</a></li>
                    <li><a href="contact.php">Contact</a></li>
                </ul>
                
                <div class="user-menu">
                    <?php if (is_logged_in()): ?>
                        <a href="cart.php" class="btn btn-info">
                            <i class="fas fa-shopping-cart"></i> Cart (<span id="cart-count"><?= count(get_cart_items()) ?></span>)
                        </a>
                        <div class="dropdown">
                            <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                            <div class="dropdown-content">
                                <?php if ($_SESSION['user_role'] === 'admin'): ?>
                                    <a href="admin/">Admin Panel</a>
                                <?php elseif ($_SESSION['user_role'] === 'sales_rep'): ?>
                                    <a href="sales_rep/">Dashboard</a>
                                <?php else: ?>
                                    <a href="customer/">My Account</a>
                                <?php endif; ?>
                                <a href="logout.php">Logout</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-primary">Login</a>
                        <a href="register.php" class="btn btn-success">Register</a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Page Header -->
        <div class="row mb-3">
            <div class="col-8">
                <h1>Our Products</h1>
                <p class="text-muted">
                    <?php if ($category): ?>
                        Showing products in "<?= htmlspecialchars($category) ?>" category
                    <?php elseif ($search): ?>
                        Search results for "<?= htmlspecialchars($search) ?>"
                    <?php else: ?>
                        Discover our complete range of premium electronics
                    <?php endif; ?>
                    (<?= count($products) ?> products found)
                </p>
            </div>
            <div class="col-4 text-right">
                <form method="GET" class="d-inline">
                    <?php if ($category): ?><input type="hidden" name="category" value="<?= htmlspecialchars($category) ?>"><?php endif; ?>
                    <?php if ($search): ?><input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>"><?php endif; ?>
                    <select name="sort" class="form-control d-inline" style="width: auto;" onchange="this.form.submit()">
                        <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>Newest First</option>
                        <option value="price_low" <?= $sort === 'price_low' ? 'selected' : '' ?>>Price: Low to High</option>
                        <option value="price_high" <?= $sort === 'price_high' ? 'selected' : '' ?>>Price: High to Low</option>
                        <option value="name" <?= $sort === 'name' ? 'selected' : '' ?>>Name A-Z</option>
                    </select>
                </form>
            </div>
        </div>

        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-3">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-filter"></i> Filters</h3>
                    </div>
                    <div class="card-body">
                        <!-- Search -->
                        <form method="GET" class="mb-3">
                            <?php if ($category): ?><input type="hidden" name="category" value="<?= htmlspecialchars($category) ?>"><?php endif; ?>
                            <div class="form-group">
                                <label class="form-label">Search Products</label>
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           value="<?= htmlspecialchars($search) ?>" placeholder="Search...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Categories -->
                        <div class="filter-section">
                            <h4>Categories</h4>
                            <div class="category-list">
                                <a href="products.php" class="category-item <?= empty($category) ? 'active' : '' ?>">
                                    <i class="fas fa-th"></i> All Products
                                </a>
                                <?php foreach ($categories as $cat): ?>
                                    <a href="products.php?category=<?= urlencode($cat) ?>" 
                                       class="category-item <?= $category === $cat ? 'active' : '' ?>">
                                        <i class="fas fa-<?= getCategoryIcon($cat) ?>"></i> <?= htmlspecialchars($cat) ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <?php if ($category || $search): ?>
                            <div class="mt-3">
                                <a href="products.php" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-9">
                <?php if (empty($products)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-search" style="font-size: 4rem; color: #ccc; margin-bottom: 2rem;"></i>
                        <h3>No products found</h3>
                        <p class="text-muted">Try adjusting your search criteria or browse all products.</p>
                        <a href="products.php" class="btn btn-primary">View All Products</a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($products as $product): ?>
                            <div class="col-4 mb-3">
                                <div class="product-card">
                                    <div class="product-image">
                                        <?php if ($product['image'] && file_exists("images/products/" . $product['image'])): ?>
                                            <img src="images/products/<?= htmlspecialchars($product['image']) ?>" 
                                                 alt="<?= htmlspecialchars($product['name']) ?>">
                                        <?php else: ?>
                                            <i class="fas fa-<?= getCategoryIcon($product['category']) ?>"></i>
                                        <?php endif; ?>
                                        <?php if ($product['featured']): ?>
                                            <div class="product-badge">Featured</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-info">
                                        <div class="product-category"><?= htmlspecialchars($product['category']) ?></div>
                                        <h3 class="product-title"><?= htmlspecialchars($product['name']) ?></h3>
                                        <p class="product-price"><?= format_currency($product['price']) ?></p>
                                        <p class="product-description">
                                            <?= htmlspecialchars(substr($product['description'], 0, 100)) ?>...
                                        </p>
                                        <div class="product-actions">
                                            <a href="product.php?id=<?= $product['id'] ?>" class="btn btn-info">
                                                <i class="fas fa-eye"></i> View Details
                                            </a>
                                            <?php if (is_logged_in()): ?>
                                                <button class="btn btn-success add-to-cart" data-product-id="<?= $product['id'] ?>">
                                                    <i class="fas fa-cart-plus"></i> Add to Cart
                                                </button>
                                            <?php else: ?>
                                                <a href="login.php" class="btn btn-success">
                                                    <i class="fas fa-sign-in-alt"></i> Login to Buy
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Griffin Gadgets</h3>
                    <p>Your trusted partner for premium electronics and cutting-edge technology.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <a href="products.php">Products</a>
                    <a href="about.php">About Us</a>
                    <a href="contact.php">Contact</a>
                </div>
                <div class="footer-section">
                    <h3>Categories</h3>
                    <?php foreach (array_slice($categories, 0, 5) as $cat): ?>
                        <a href="products.php?category=<?= urlencode($cat) ?>"><?= htmlspecialchars($cat) ?></a>
                    <?php endforeach; ?>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-phone"></i> +234 800 GRIFFIN</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Griffin Gadgets. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <style>
        .category-list {
            display: flex;
            flex-direction: column;
        }
        .category-item {
            display: block;
            padding: 0.5rem 0.75rem;
            color: #333;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 0.25rem;
            transition: all 0.3s ease;
        }
        .category-item:hover,
        .category-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .product-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff6b6b;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .product-category {
            font-size: 0.875rem;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .input-group {
            display: flex;
        }
        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        .input-group .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        /* CRITICAL FIX: PREVENT TEXT OVERLAY ON IMAGES */
        /* This CSS forces the proper layout and prevents any text from appearing over images */
        .product-card {
            background: white !important;
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
            transition: all 0.3s ease !important;
            height: 480px !important;
            display: flex !important;
            flex-direction: column !important;
            position: relative !important;
        }

        .product-image {
            width: 100% !important;
            height: 180px !important;
            background: #f8f9fa !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: #ccc !important;
            font-size: 1.2rem !important;
            position: relative !important;
            flex-shrink: 0 !important;
            border-bottom: 1px solid #eee !important;
        }

        .product-image img {
            width: 80% !important;
            height: 80% !important;
            object-fit: contain !important;
            border-radius: 8px !important;
            aspect-ratio: 1/1 !important;
        }

        .product-info {
            padding: 1.25rem !important;
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            min-height: 0 !important;
            position: relative !important;
            background: white !important;
            z-index: 1 !important;
        }

        .product-title {
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
            color: #333 !important;
            line-height: 1.3 !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
        }

        .product-price {
            font-size: 1.3rem !important;
            font-weight: 700 !important;
            color: #667eea !important;
            margin-bottom: 0.75rem !important;
        }

        .product-description {
            color: #666 !important;
            margin-bottom: 0.75rem !important;
            font-size: 0.85rem !important;
            line-height: 1.4 !important;
            flex: 1 !important;
            overflow: hidden !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 3 !important;
            -webkit-box-orient: vertical !important;
        }

        .product-actions {
            display: flex !important;
            gap: 0.5rem !important;
            margin-top: auto !important;
            padding-top: 0.5rem !important;
        }

        .product-actions .btn {
            flex: 1 !important;
            font-size: 0.85rem !important;
            padding: 0.5rem 0.75rem !important;
        }

        /* CLEAN LAYOUT: Remove debug borders and ensure clean separation */
        .product-image {
            border-bottom: 2px solid #f0f0f0 !important; /* Subtle separation line */
        }

        .product-info {
            background: white !important; /* Ensure white background */
        }

        /* Ensure no absolute positioning anywhere */
        .product-card * {
            position: static !important;
        }

        .product-card .product-image {
            position: relative !important;
        }

        .product-card .product-info {
            position: relative !important;
        }
    </style>
</body>
</html>

<?php
function getCategoryIcon($category) {
    $icons = [
        'Smartphones' => 'mobile-alt',
        'Laptops' => 'laptop',
        'Tablets' => 'tablet-alt',
        'Audio' => 'headphones',
        'Wearables' => 'watch',
        'Accessories' => 'plug'
    ];
    return $icons[$category] ?? 'cube';
}
?>
