<?php
require_once '../includes/functions.php';

// Require admin login
require_role('admin');

$user = get_logged_in_user();

// Get analytics data
global $pdo;

// Revenue analytics
$stmt = $pdo->query("SELECT SUM(total_amount) as total_revenue FROM orders WHERE status != 'cancelled'");
$total_revenue = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT SUM(total_amount) as monthly_revenue FROM orders WHERE status != 'cancelled' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$monthly_revenue = $stmt->fetchColumn() ?: 0;

// Order analytics
$stmt = $pdo->query("SELECT COUNT(*) as total_orders FROM orders");
$total_orders = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) as monthly_orders FROM orders WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$monthly_orders = $stmt->fetchColumn();

// Top selling products
$stmt = $pdo->prepare("
    SELECT p.name, p.price, SUM(oi.quantity) as total_sold, SUM(oi.subtotal) as total_revenue
    FROM order_items oi
    JOIN products p ON oi.product_id = p.id
    JOIN orders o ON oi.order_id = o.id
    WHERE o.status != 'cancelled'
    GROUP BY p.id
    ORDER BY total_sold DESC
    LIMIT 5
");
$stmt->execute();
$top_products = $stmt->fetchAll();

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Griffin Gadgets Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="users.php">Users</a></li>
                    <li><a href="orders.php">Orders</a></li>
                    <li><a href="analytics.php" class="active">Analytics</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Admin: <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Page Header -->
        <div class="card mb-3">
            <div class="card-body">
                <h1><i class="fas fa-chart-line"></i> Sales Analytics</h1>
                <p class="text-muted">Monitor your business performance and revenue trends.</p>
            </div>
        </div>

        <!-- Revenue Overview -->
        <div class="row mb-3">
            <div class="col-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="icon mb-3" style="font-size: 3rem; color: #28a745;">
                            <i class="fas fa-naira-sign"></i>
                        </div>
                        <h2 class="text-success"><?= format_currency($total_revenue) ?></h2>
                        <p class="mb-0">Total Revenue</p>
                        <small class="text-muted">All time</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="icon mb-3" style="font-size: 3rem; color: #007bff;">
                            <i class="fas fa-calendar-month"></i>
                        </div>
                        <h2 class="text-primary"><?= format_currency($monthly_revenue) ?></h2>
                        <p class="mb-0">This Month</p>
                        <small class="text-muted"><?= date('F Y') ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Statistics -->
        <div class="row mb-3">
            <div class="col-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="icon mb-3" style="font-size: 3rem; color: #6f42c1;">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <h2 class="text-purple"><?= number_format($total_orders) ?></h2>
                        <p class="mb-0">Total Orders</p>
                        <small class="text-muted">All time</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="icon mb-3" style="font-size: 3rem; color: #fd7e14;">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h2 class="text-warning"><?= number_format($monthly_orders) ?></h2>
                        <p class="mb-0">Orders This Month</p>
                        <small class="text-muted"><?= date('F Y') ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Selling Products -->
        <div class="card mb-3">
            <div class="card-header">
                <h3><i class="fas fa-trophy"></i> Top Selling Products</h3>
            </div>
            <div class="card-body">
                <?php if (empty($top_products)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-chart-bar" style="font-size: 4rem; color: #ccc; margin-bottom: 2rem;"></i>
                        <h4>No sales data available</h4>
                        <p class="text-muted">Sales analytics will appear here once customers start purchasing.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Rank</th>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Units Sold</th>
                                    <th>Total Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_products as $index => $product): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-<?= $index === 0 ? 'warning' : ($index === 1 ? 'secondary' : 'info') ?>">
                                                #<?= $index + 1 ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($product['name']) ?></strong>
                                        </td>
                                        <td><?= format_currency($product['price']) ?></td>
                                        <td>
                                            <span class="badge bg-success"><?= number_format($product['total_sold']) ?> units</span>
                                        </td>
                                        <td>
                                            <strong><?= format_currency($product['total_revenue']) ?></strong>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row mb-3">
            <div class="col-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info">
                            <?= $total_orders > 0 ? format_currency($total_revenue / $total_orders) : format_currency(0) ?>
                        </h4>
                        <p class="mb-0">Average Order Value</p>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success">
                            <?php
                            $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM orders WHERE status != 'cancelled'");
                            $customers_with_orders = $stmt->fetchColumn();
                            echo number_format($customers_with_orders);
                            ?>
                        </h4>
                        <p class="mb-0">Customers with Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning">
                            <?php
                            $stmt = $pdo->query("SELECT AVG(item_count) FROM (SELECT COUNT(*) as item_count FROM order_items oi JOIN orders o ON oi.order_id = o.id WHERE o.status != 'cancelled' GROUP BY o.id) as order_sizes");
                            $avg_items = $stmt->fetchColumn() ?: 0;
                            echo number_format($avg_items, 1);
                            ?>
                        </h4>
                        <p class="mb-0">Avg Items per Order</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-3">
                        <a href="orders.php" class="btn btn-outline-primary d-block">
                            <i class="fas fa-list"></i> View All Orders
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="products.php" class="btn btn-outline-success d-block">
                            <i class="fas fa-cube"></i> Manage Products
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="users.php" class="btn btn-outline-info d-block">
                            <i class="fas fa-users"></i> View Customers
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="index.php" class="btn btn-outline-secondary d-block">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
