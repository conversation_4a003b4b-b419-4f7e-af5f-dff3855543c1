<?php
require_once 'includes/functions.php';

$error = '';
$success = '';
$step = 1; // 1: Enter username, 2: Answer security question 1, 3: Answer security question 2, 4: Reset password

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['step'])) {
        $step = (int)$_POST['step'];
        
        if ($step === 1) {
            // Step 1: Check username and get security questions
            $username = sanitize_input($_POST['username'] ?? '');

            if (empty($username)) {
                $error = 'Please enter your username or email';
            } else {
                global $pdo;
                $stmt = $pdo->prepare("SELECT id, username, security_question, security_question2 FROM users WHERE (username = ? OR email = ?) AND status = 'active'");
                $stmt->execute([$username, $username]);
                $user = $stmt->fetch();

                if ($user && !empty($user['security_question']) && !empty($user['security_question2'])) {
                    $_SESSION['reset_user_id'] = $user['id'];
                    $_SESSION['reset_username'] = $user['username'];
                    $step = 2;
                } else {
                    $error = 'User not found or security questions not set. Please contact support.';
                }
            }
        } elseif ($step === 2) {
            // Step 2: Verify first security answer
            $security_answer1 = sanitize_input($_POST['security_answer1'] ?? '');

            if (empty($security_answer1)) {
                $error = 'Please provide an answer to the first security question';
            } else {
                global $pdo;
                $stmt = $pdo->prepare("SELECT security_answer FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['reset_user_id']]);
                $stored_answer = $stmt->fetchColumn();

                if (password_verify($security_answer1, $stored_answer)) {
                    $step = 3;
                } else {
                    $error = 'Incorrect answer to first security question';
                }
            }
        } elseif ($step === 3) {
            // Step 3: Verify second security answer
            $security_answer2 = sanitize_input($_POST['security_answer2'] ?? '');

            if (empty($security_answer2)) {
                $error = 'Please provide an answer to the second security question';
            } else {
                global $pdo;
                $stmt = $pdo->prepare("SELECT security_answer2 FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['reset_user_id']]);
                $stored_answer2 = $stmt->fetchColumn();

                if (password_verify($security_answer2, $stored_answer2)) {
                    $step = 4;
                } else {
                    $error = 'Incorrect answer to second security question';
                }
            }
        } elseif ($step === 4) {
            // Step 4: Reset password
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($new_password) || empty($confirm_password)) {
                $error = 'Please fill in all fields';
            } elseif ($new_password !== $confirm_password) {
                $error = 'Passwords do not match';
            } elseif (strlen($new_password) < 6) {
                $error = 'Password must be at least 6 characters long';
            } else {
                global $pdo;
                $hashed_password = hash_password($new_password);
                $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                
                if ($stmt->execute([$hashed_password, $_SESSION['reset_user_id']])) {
                    unset($_SESSION['reset_user_id'], $_SESSION['reset_username']);
                    $success = 'Password reset successfully! You can now login with your new password.';
                    $step = 1;
                } else {
                    $error = 'Error resetting password. Please try again.';
                }
            }
        }
    }
}

// Get security questions for steps 2 and 3
$security_question1 = '';
$security_question2 = '';
if (($step === 2 || $step === 3) && isset($_SESSION['reset_user_id'])) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT security_question, security_question2 FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['reset_user_id']]);
    $questions = $stmt->fetch();
    $security_question1 = $questions['security_question'] ?? '';
    $security_question2 = $questions['security_question2'] ?? '';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .auth-card {
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .auth-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .auth-header p {
            color: #666;
        }
        .auth-links {
            text-align: center;
            margin-top: 2rem;
        }
        .auth-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
        }
        .auth-links a:hover {
            text-decoration: underline;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: #666;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }

        /* Password toggle styles */
        .password-field {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
            z-index: 10;
        }
        .password-toggle:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1><i class="fas fa-bolt"></i> Griffin Gadgets</h1>
                <p>Reset your password</p>
            </div>

            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : '' ?>">1</div>
                <div class="step <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : '' ?>">2</div>
                <div class="step <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : '' ?>">3</div>
                <div class="step <?= $step >= 4 ? 'active' : '' ?>">4</div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($step === 1): ?>
                <!-- Step 1: Enter Username -->
                <form method="POST">
                    <input type="hidden" name="step" value="1">
                    
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i> Username or Email
                        </label>
                        <input type="text" id="username" name="username" class="form-control" 
                               placeholder="Enter your username or email" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-arrow-right"></i> Continue
                        </button>
                    </div>
                </form>

            <?php elseif ($step === 2): ?>
                <!-- Step 2: First Security Question -->
                <form method="POST">
                    <input type="hidden" name="step" value="2">

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-question-circle"></i> Security Question 1
                        </label>
                        <div class="form-control" style="background: #f8f9fa; border: none;">
                            <?= htmlspecialchars($security_question1) ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="security_answer1" class="form-label">
                            <i class="fas fa-key"></i> Your Answer
                        </label>
                        <input type="text" id="security_answer1" name="security_answer1" class="form-control"
                               placeholder="Enter your answer" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-arrow-right"></i> Continue
                        </button>
                    </div>
                </form>

            <?php elseif ($step === 3): ?>
                <!-- Step 3: Second Security Question -->
                <form method="POST">
                    <input type="hidden" name="step" value="3">

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-question-circle"></i> Security Question 2
                        </label>
                        <div class="form-control" style="background: #f8f9fa; border: none;">
                            <?= htmlspecialchars($security_question2) ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="security_answer2" class="form-label">
                            <i class="fas fa-key"></i> Your Answer
                        </label>
                        <input type="text" id="security_answer2" name="security_answer2" class="form-control"
                               placeholder="Enter your answer" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-arrow-right"></i> Verify Answer
                        </button>
                    </div>
                </form>

            <?php elseif ($step === 4): ?>
                <!-- Step 4: Reset Password -->
                <form method="POST">
                    <input type="hidden" name="step" value="4">
                    
                    <div class="form-group">
                        <label for="new_password" class="form-label">
                            <i class="fas fa-lock"></i> New Password
                        </label>
                        <div class="password-field">
                            <input type="password" id="new_password" name="new_password" class="form-control"
                                   data-strength required>
                            <i class="fas fa-eye password-toggle" onclick="togglePassword('new_password')"></i>
                        </div>
                        <div id="new_password-strength" class="password-strength"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-lock"></i> Confirm New Password
                        </label>
                        <div class="password-field">
                            <input type="password" id="confirm_password" name="confirm_password"
                                   class="form-control" data-confirm-password="new_password" required>
                            <i class="fas fa-eye password-toggle" onclick="togglePassword('confirm_password')"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-success" style="width: 100%;">
                            <i class="fas fa-check"></i> Reset Password
                        </button>
                    </div>
                </form>
            <?php endif; ?>

            <div class="auth-links">
                <a href="login.php">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const toggle = field.nextElementSibling;

            if (field.type === 'password') {
                field.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
