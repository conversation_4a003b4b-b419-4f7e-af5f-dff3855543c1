/* Griffin Gadgets - Premium Professional E-commerce Design */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Import Bootstrap */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: #f8fafc;
    font-weight: 400;
    overflow-x: hidden;
}

/* Premium Container */
.main-content {
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 50px rgba(0,0,0,0.1);
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -15px;
}

.col {
    flex: 1;
    padding: 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-12 { flex: 0 0 100%; }

/* Premium Header Design */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: #1a1a1a;
    padding: 1.5rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.logo i {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.8rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
}

.nav-links a {
    color: #1a1a1a;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    position: relative;
}

.nav-links a:hover,
.nav-links a.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-menu span {
    color: #1a1a1a;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Premium Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-family: 'Playfair Display', serif;
    font-size: 4.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    font-weight: 400;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    margin-bottom: 4rem;
}

.hero-actions .btn {
    margin: 0 1rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    color: white;
}

.hero p {
    font-size: 1.4rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

/* Cards */
.card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 2rem;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.card-header {
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    border: none;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"/><circle cx="80" cy="30" r="15" fill="url(%23a)"/><circle cx="40" cy="70" r="12" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.card-header h1,
.card-header h2,
.card-header h3 {
    position: relative;
    z-index: 2;
    margin: 0;
    font-family: 'Playfair Display', serif;
}

.card-body {
    padding: 2.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 480px; /* Reduced height for better proportions */
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 180px; /* Significantly reduced from 250px */
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ccc;
    font-size: 1.2rem;
    position: relative;
    flex-shrink: 0; /* Prevent shrinking */
    border-bottom: 1px solid #eee; /* Clear separation */
}

.product-image img {
    width: 80%; /* Reduced from 90% */
    height: 80%; /* Reduced from 90% */
    object-fit: contain;
    border-radius: 8px;
    aspect-ratio: 1/1; /* Force square aspect ratio for uniformity */
}

.product-info {
    padding: 1.25rem; /* Slightly reduced padding */
    flex: 1; /* Take remaining space */
    display: flex;
    flex-direction: column;
    min-height: 0; /* Prevent flex item from overflowing */
}

.product-title {
    font-size: 1.1rem; /* Slightly smaller */
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
    line-height: 1.3; /* Better line spacing */
    overflow: hidden; /* Prevent overflow */
    text-overflow: ellipsis; /* Add ellipsis for long titles */
}

.product-price {
    font-size: 1.3rem; /* Slightly smaller */
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.75rem; /* Reduced margin */
}

.product-description {
    color: #666;
    margin-bottom: 0.75rem; /* Reduced margin */
    font-size: 0.85rem; /* Smaller text */
    line-height: 1.4; /* Better readability */
    flex: 1; /* Take available space */
    overflow: hidden; /* Prevent overflow */
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Limit to 3 lines */
    -webkit-box-orient: vertical;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: auto; /* Push to bottom */
    padding-top: 0.5rem; /* Add separation */
}

.product-actions .btn {
    flex: 1;
    font-size: 0.85rem; /* Smaller button text */
    padding: 0.5rem 0.75rem; /* Smaller buttons */
}

/* Premium Product Cards */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    font-weight: 400;
}

.premium-product-card {
    max-width: 300px; /* Slightly smaller */
    margin: 0 auto;
    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
    border-radius: 16px;
    overflow: hidden;
    background: #fff;
    transition: box-shadow 0.3s;
    height: 420px; /* Reduced height */
    display: flex;
    flex-direction: column;
}

.product-image-container {
    height: 200px !important; /* Further reduced from 250px */
    width: 100% !important;
    max-width: 100% !important;
    background: #fff !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    position: relative !important;
    border: 1px solid #eee !important;
    border-bottom: 2px solid #f0f0f0 !important; /* Clear separation */
    flex-shrink: 0; /* Prevent shrinking */
}

.product-image-container .product-image {
    width: 80% !important; /* Reduced from 90% */
    height: 80% !important; /* Reduced from 90% */
    max-width: 80% !important;
    max-height: 80% !important;
    object-fit: contain !important;
    background: transparent !important;
    display: block !important;
    margin: 0 auto !important;
    aspect-ratio: 1/1 !important; /* Force square aspect ratio */
}

.premium-product-card:hover .product-image-container .product-image {
    transform: scale(1.05);
}

.product-placeholder {
    text-align: center;
    color: #ccc;
}

.product-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.premium-product-card:hover .product-overlay {
    opacity: 1;
}

.product-content {
    padding: 1.25rem; /* Slightly reduced padding */
    flex: 1; /* Take remaining space */
    display: flex;
    flex-direction: column;
    min-height: 0; /* Prevent overflow */
}

.product-category {
    font-size: 0.8rem; /* Slightly smaller */
    color: #667eea;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.4rem; /* Reduced margin */
}

.product-content .product-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem; /* Reduced from 1.3rem */
    font-weight: 600;
    margin-bottom: 0.6rem; /* Reduced margin */
    color: #1a1a1a;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-content .product-description {
    color: #666;
    margin-bottom: 0.75rem; /* Reduced margin */
    line-height: 1.4;
    font-size: 0.85rem; /* Smaller text */
    flex: 1; /* Take available space */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Limit to 2 lines */
    -webkit-box-orient: vertical;
}

.product-price-section {
    margin-bottom: 0.75rem; /* Reduced margin */
    margin-top: auto; /* Push to bottom */
}

.product-price-section .product-price {
    font-size: 1.6rem; /* Reduced from 1.75rem */
    font-weight: 700;
    color: #1a1a1a;
    display: block;
    margin-bottom: 0.25rem;
}

.price-label {
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
}

.product-content .product-actions {
    display: flex;
    gap: 0.75rem; /* Reduced gap */
    padding-top: 0.5rem; /* Add separation */
}

.product-content .product-actions .btn {
    flex: 1;
    font-weight: 600;
    font-size: 0.85rem; /* Smaller button text */
    padding: 0.6rem 1rem; /* Adjusted padding */
}

/* Feature Cards */
.feature-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1.5rem;
}

.feature-icon.shipping {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.feature-icon.support {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.feature-icon.warranty {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.feature-icon.secure {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.feature-card h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #1a1a1a;
}

.feature-card p {
    color: #666;
    line-height: 1.7;
    font-size: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #667eea;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* CRITICAL FIX: Prevent text-image collision with high specificity */
.product-card .product-image {
    width: 100% !important;
    height: 180px !important;
    background: #f8f9fa !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    flex-shrink: 0 !important;
    border-bottom: 2px solid #f0f0f0 !important;
}

.product-card .product-image img {
    width: 80% !important;
    height: 80% !important;
    object-fit: contain !important;
    aspect-ratio: 1/1 !important;
}

.product-card .product-info {
    padding: 1.25rem !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
    background: white !important;
    z-index: 1 !important;
}

.product-card .product-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    color: #333 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.product-card .product-price {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #667eea !important;
    margin-bottom: 0.75rem !important;
}

.product-card .product-description {
    color: #666 !important;
    margin-bottom: 0.75rem !important;
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
    flex: 1 !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
}

.product-card .product-actions {
    display: flex !important;
    gap: 0.5rem !important;
    margin-top: auto !important;
    padding-top: 0.5rem !important;
}

.product-card .product-actions .btn {
    flex: 1 !important;
    font-size: 0.85rem !important;
    padding: 0.5rem 0.75rem !important;
}

/* Ensure no absolute positioning overrides */
.product-card * {
    position: static !important;
}

.product-card .product-image,
.product-card .product-info {
    position: relative !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .row {
        flex-direction: column;
    }
    
    .col-3,
    .col-4,
    .col-6 {
        flex: 0 0 100%;
    }
}

/* Dashboard Cards - Colorful and Clickable */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-decoration: none;
    display: block;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border: none;
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.25);
    color: white;
    text-decoration: none;
}

.dashboard-card.users {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-card.products {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.dashboard-card.orders {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.dashboard-card.revenue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.dashboard-card.warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.notification-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.dashboard-card p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.dashboard-card .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* Quick Action Cards */
.quick-action-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: none;
    height: 100%;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.quick-action-card.add-product {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.quick-action-card.approve-users {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.quick-action-card.view-orders {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.quick-action-card.system-reset {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.quick-action-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.quick-action-card span {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

@media (min-width: 992px) {
    .row.g-4 > [class^='col-'] {
        flex: 0 0 33.3333%;
        max-width: 33.3333%;
    }
}
