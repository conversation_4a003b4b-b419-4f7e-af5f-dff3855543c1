<?php
require_once 'db_connect.php';

// Security Functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// User Authentication Functions
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

function is_logged_in() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

function require_login() {
    if (!is_logged_in()) {
        header('Location: login.php');
        exit();
    }
}

function require_role($required_role) {
    require_login();
    if ($_SESSION['user_role'] !== $required_role) {
        header('Location: unauthorized.php');
        exit();
    }
}

function get_current_user() {
    global $pdo;
    if (!is_logged_in()) {
        return null;
    }

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

// User Management Functions
function create_user($username, $email, $password, $role = 'customer', $full_name = '', $phone = '') {
    global $pdo;

    $hashed_password = hash_password($password);
    $status = ($role === 'customer') ? 'active' : 'pending';

    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role, full_name, phone, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
    return $stmt->execute([$username, $email, $hashed_password, $role, $full_name, $phone, $status]);
}

function authenticate_user($username, $password) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'");
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch();

    if ($user && verify_password($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['username'] = $user['username'];
        return true;
    }
    return false;
}

function logout_user() {
    session_destroy();
    header('Location: index.php');
    exit();
}

// Product Functions
function get_products($category = null, $limit = null, $featured = false) {
    global $pdo;

    $sql = "SELECT * FROM products WHERE status = 'active'";
    $params = [];

    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }

    if ($featured) {
        $sql .= " AND featured = 1";
    }

    $sql .= " ORDER BY created_at DESC";

    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

function get_product($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function get_categories() {
    global $pdo;

    $stmt = $pdo->query("SELECT DISTINCT category FROM products WHERE status = 'active' ORDER BY category");
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

// Cart Functions
function add_to_cart($product_id, $quantity = 1) {
    if (!is_logged_in()) {
        return false;
    }

    global $pdo;

    // Check if item already in cart
    $stmt = $pdo->prepare("SELECT * FROM cart WHERE user_id = ? AND product_id = ?");
    $stmt->execute([$_SESSION['user_id'], $product_id]);
    $existing = $stmt->fetch();

    if ($existing) {
        // Update quantity
        $stmt = $pdo->prepare("UPDATE cart SET quantity = quantity + ? WHERE user_id = ? AND product_id = ?");
        return $stmt->execute([$quantity, $_SESSION['user_id'], $product_id]);
    } else {
        // Add new item
        $stmt = $pdo->prepare("INSERT INTO cart (user_id, product_id, quantity, created_at) VALUES (?, ?, ?, NOW())");
        return $stmt->execute([$_SESSION['user_id'], $product_id, $quantity]);
    }
}

function get_cart_items() {
    if (!is_logged_in()) {
        return [];
    }

    global $pdo;

    $stmt = $pdo->prepare("
        SELECT c.*, p.name, p.price, p.image, (c.quantity * p.price) as subtotal
        FROM cart c
        JOIN products p ON c.product_id = p.id
        WHERE c.user_id = ? AND p.status = 'active'
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetchAll();
}

function get_cart_total() {
    $items = get_cart_items();
    $total = 0;
    foreach ($items as $item) {
        $total += $item['subtotal'];
    }
    return $total;
}

function clear_cart() {
    if (!is_logged_in()) {
        return false;
    }

    global $pdo;

    $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
    return $stmt->execute([$_SESSION['user_id']]);
}

// Order Functions
function create_order($payment_method = 'paystack', $payment_reference = '') {
    if (!is_logged_in()) {
        return false;
    }

    global $pdo;

    $cart_items = get_cart_items();
    if (empty($cart_items)) {
        return false;
    }

    $total = get_cart_total();
    $order_number = 'GG' . date('Ymd') . rand(1000, 9999);

    try {
        $pdo->beginTransaction();

        // Create order
        $stmt = $pdo->prepare("INSERT INTO orders (user_id, order_number, total_amount, payment_method, payment_reference, status, created_at) VALUES (?, ?, ?, ?, ?, 'pending', NOW())");
        $stmt->execute([$_SESSION['user_id'], $order_number, $total, $payment_method, $payment_reference]);
        $order_id = $pdo->lastInsertId();

        // Create order items
        foreach ($cart_items as $item) {
            $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price, subtotal) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$order_id, $item['product_id'], $item['quantity'], $item['price'], $item['subtotal']]);
        }

        // Clear cart
        clear_cart();

        $pdo->commit();
        return $order_id;
    } catch (Exception $e) {
        $pdo->rollback();
        return false;
    }
}

// Utility Functions
function format_currency($amount) {
    return '₦' . number_format($amount, 2);
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function flash_message($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}
?>