<?php
require_once 'includes/functions.php';

// Require login
require_login();

$order_number = sanitize_input($_GET['order'] ?? '');
if (empty($order_number)) {
    flash_message('Invalid order reference', 'danger');
    redirect('cart.php');
}

// Get order details
global $pdo;
$stmt = $pdo->prepare("SELECT * FROM orders WHERE order_number = ? AND user_id = ?");
$stmt->execute([$order_number, $_SESSION['user_id']]);
$order = $stmt->fetch();

if (!$order) {
    flash_message('Order not found', 'danger');
    redirect('cart.php');
}

$user = get_logged_in_user();

// Paystack configuration (use test keys for demo)
$paystack_public_key = 'pk_test_your_paystack_public_key_here';
$paystack_secret_key = 'sk_test_your_paystack_secret_key_here';

// Convert amount to kobo (Paystack uses kobo)
$amount_in_kobo = $order['total_amount'] * 100;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://js.paystack.co/v1/inline.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="#" class="active">Payment</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                </div>
            </nav>
        </div>
    </header>

    <div class="container mt-3">
        <!-- Payment Progress -->
        <div class="checkout-progress mb-3">
            <div class="progress-step completed">
                <i class="fas fa-shopping-cart"></i>
                <span>Cart</span>
            </div>
            <div class="progress-step completed">
                <i class="fas fa-credit-card"></i>
                <span>Checkout</span>
            </div>
            <div class="progress-step active">
                <i class="fas fa-lock"></i>
                <span>Payment</span>
            </div>
            <div class="progress-step">
                <i class="fas fa-check-circle"></i>
                <span>Complete</span>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h2><i class="fas fa-credit-card"></i> Secure Payment</h2>
                    </div>
                    <div class="card-body text-center">
                        <!-- Order Summary -->
                        <div class="payment-summary">
                            <h3>Order #<?= htmlspecialchars($order['order_number']) ?></h3>
                            <div class="amount-display">
                                <span class="currency">₦</span>
                                <span class="amount"><?= number_format($order['total_amount'], 2) ?></span>
                            </div>
                            <p class="text-muted">Payment for your Griffin Gadgets order</p>
                        </div>

                        <!-- Payment Button -->
                        <div class="payment-actions">
                            <button id="paystack-button" class="btn btn-success btn-lg">
                                <i class="fas fa-lock"></i> Pay with Paystack
                            </button>
                            
                            <div class="payment-security mt-3">
                                <p class="text-muted small">
                                    <i class="fas fa-shield-alt"></i>
                                    Your payment is secured with 256-bit SSL encryption
                                </p>
                            </div>
                        </div>

                        <!-- Alternative Actions -->
                        <div class="alternative-actions mt-4">
                            <a href="checkout.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Checkout
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <h5>We Accept</h5>
                        <div class="payment-methods-display">
                            <i class="fab fa-cc-visa"></i>
                            <i class="fab fa-cc-mastercard"></i>
                            <i class="fas fa-university"></i>
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <p class="text-muted small mt-2">
                            Visa, Mastercard, Bank Transfer, and Mobile Money
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('paystack-button').addEventListener('click', function() {
            // Initialize Paystack payment
            var handler = PaystackPop.setup({
                key: '<?= $paystack_public_key ?>', // Replace with your public key
                email: '<?= htmlspecialchars($user['email']) ?>',
                amount: <?= $amount_in_kobo ?>, // Amount in kobo
                currency: 'NGN',
                ref: '<?= $order['order_number'] ?>_' + Math.floor((Math.random() * **********) + 1),
                metadata: {
                    order_number: '<?= $order['order_number'] ?>',
                    customer_name: '<?= htmlspecialchars($user['full_name']) ?>',
                    customer_phone: '<?= htmlspecialchars($user['phone']) ?>'
                },
                callback: function(response) {
                    // Payment successful
                    window.location.href = 'payment-success.php?reference=' + response.reference + '&order=' + '<?= $order['order_number'] ?>';
                },
                onClose: function() {
                    // Payment cancelled
                    alert('Payment cancelled. You can try again or contact support if you need help.');
                }
            });
            
            handler.openIframe();
        });

        // Demo mode notice
        <?php if (strpos($paystack_public_key, 'your_paystack') !== false): ?>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('paystack-button').addEventListener('click', function(e) {
                e.preventDefault();
                alert('Demo Mode: Paystack integration requires valid API keys. Redirecting to success page for demo purposes.');
                window.location.href = 'payment-success.php?reference=demo_' + Date.now() + '&order=<?= $order['order_number'] ?>';
            });
        });
        <?php endif; ?>
    </script>

    <style>
        .checkout-progress {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            margin: 0 2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .progress-step i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .progress-step.completed {
            background: #28a745;
            color: white;
        }
        .progress-step.active {
            background: #667eea;
            color: white;
        }
        .progress-step:not(.completed):not(.active) {
            background: #f8f9fa;
            color: #6c757d;
        }
        .payment-summary {
            margin-bottom: 2rem;
        }
        .amount-display {
            font-size: 3rem;
            font-weight: bold;
            color: #28a745;
            margin: 1rem 0;
        }
        .currency {
            font-size: 2rem;
            vertical-align: top;
        }
        .payment-methods-display {
            display: flex;
            justify-content: center;
            gap: 1rem;
            font-size: 2rem;
            color: #666;
            margin: 1rem 0;
        }
        .justify-content-center {
            justify-content: center;
        }
        .payment-security {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
    </style>
</body>
</html>
