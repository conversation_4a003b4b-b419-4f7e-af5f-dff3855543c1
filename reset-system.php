<?php
require_once 'includes/db_connect.php';

$message = '';
$error = '';
$confirmation_required = true;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $confirm_reset = $_POST['confirm_reset'] ?? '';
    $admin_password = $_POST['admin_password'] ?? '';
    
    if ($confirm_reset !== 'RESET_GRIFFIN_GADGETS') {
        $error = 'Please type the exact confirmation phrase to proceed';
    } elseif (empty($admin_password)) {
        $error = 'Please provide the new admin password';
    } elseif (strlen($admin_password) < 6) {
        $error = 'Admin password must be at least 6 characters long';
    } else {
        try {
            // Start transaction
            $pdo->beginTransaction();
            
            // Clear all tables
            $tables = ['order_items', 'orders', 'cart', 'products', 'users'];
            
            foreach ($tables as $table) {
                $pdo->exec("DELETE FROM $table");
                $pdo->exec("ALTER TABLE $table AUTO_INCREMENT = 1");
            }
            
            // Create new admin user
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role, full_name, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute(['admin', '<EMAIL>', $hashed_password, 'admin', 'System Administrator', 'active']);
            
            // Insert sample products
            $sample_products = [
                ['iPhone 13 Pro', 'iPhone 13 Pro with ProRes video recording and ProMotion display', 1700000, 'Smartphones', '13pro.JPG', 25, 1],
                ['iPhone 14 Pro', 'iPhone 14 Pro with Dynamic Island and 48MP camera', 2400000, 'Smartphones', '14pro.JPG', 30, 1],
                ['iPhone 15 Pro', 'Latest iPhone with A17 Pro chip, titanium design, and advanced camera system', 2600000, 'Smartphones', '15pro.jpg', 50, 1]
            ];

            $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category, image, stock_quantity, featured, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            
            foreach ($sample_products as $product) {
                $stmt->execute($product);
            }
            
            // Commit transaction
            $pdo->commit();
            
            $message = 'System reset completed successfully! All data has been cleared and sample data has been restored. New admin credentials: admin / ' . $admin_password;
            $confirmation_required = false;
            
        } catch (Exception $e) {
            $pdo->rollback();
            $error = 'Error resetting system: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Reset - Griffin Gadgets</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .reset-container {
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 500px;
        }
        .reset-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .reset-header h1 {
            color: #dc3545;
            margin-bottom: 0.5rem;
        }
        .reset-header p {
            color: #666;
            margin: 0;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        .form-control:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            width: 100%;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .danger-box {
            background: #f8d7da;
            border: 2px solid #dc3545;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            color: #721c24;
        }
        .danger-box h3 {
            margin: 0 0 1rem 0;
            color: #dc3545;
        }
        .danger-box ul {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }
        .danger-box li {
            margin-bottom: 0.5rem;
        }
        .confirmation-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #856404;
            text-align: center;
            font-weight: bold;
            font-family: monospace;
            font-size: 1.2rem;
        }
        .links {
            text-align: center;
            margin-top: 2rem;
        }
        .links a {
            color: #dc3545;
            text-decoration: none;
            margin: 0 1rem;
        }
        .links a:hover {
            text-decoration: underline;
        }
        .success-actions {
            text-align: center;
            margin-top: 2rem;
        }
        .success-actions .btn {
            width: auto;
            margin: 0 0.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h1>⚠️ SYSTEM RESET</h1>
            <p>Complete database and system reset utility</p>
        </div>

        <?php if ($confirmation_required): ?>
            <div class="danger-box">
                <h3>🚨 DANGER - IRREVERSIBLE ACTION</h3>
                <p>This action will completely reset the Griffin Gadgets system and:</p>
                <ul>
                    <li><strong>DELETE ALL USERS</strong> (except new admin)</li>
                    <li><strong>DELETE ALL ORDERS</strong> and order history</li>
                    <li><strong>DELETE ALL PRODUCTS</strong> (will restore sample products)</li>
                    <li><strong>CLEAR ALL SHOPPING CARTS</strong></li>
                    <li><strong>RESET ALL AUTO-INCREMENT COUNTERS</strong></li>
                </ul>
                <p><strong>This action cannot be undone!</strong></p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="confirm_reset" class="form-label">Type the following phrase to confirm:</label>
                    <div class="confirmation-box">RESET_GRIFFIN_GADGETS</div>
                    <input type="text" id="confirm_reset" name="confirm_reset" class="form-control" 
                           placeholder="Type the exact phrase above" required>
                </div>

                <div class="form-group">
                    <label for="admin_password" class="form-label">New Admin Password</label>
                    <input type="password" id="admin_password" name="admin_password" class="form-control" 
                           placeholder="Enter password for new admin account" required>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to reset the entire system? This cannot be undone!')">
                        🔥 RESET ENTIRE SYSTEM
                    </button>
                </div>
            </form>

        <?php else: ?>
            <div class="alert alert-success">
                <?= htmlspecialchars($message) ?>
            </div>

            <div class="success-actions">
                <a href="index.php" class="btn btn-primary">Go to Homepage</a>
                <a href="login.php" class="btn btn-success">Login as Admin</a>
            </div>
        <?php endif; ?>

        <div class="links">
            <a href="index.php">← Back to Home</a>
            <a href="importdb.php">Database Setup</a>
        </div>

        <div class="danger-box" style="margin-top: 2rem;">
            <p><strong>Security Notice:</strong> This is a powerful system utility. In production environments, this file should be removed or protected with additional authentication.</p>
        </div>
    </div>
</body>
</html>
