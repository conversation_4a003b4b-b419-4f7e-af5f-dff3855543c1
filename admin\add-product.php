<?php
require_once '../includes/functions.php';

// Require admin login
require_role('admin');

$user = get_logged_in_user();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    $price = (float)($_POST['price'] ?? 0);
    $category = sanitize_input($_POST['category'] ?? '');
    $image = sanitize_input($_POST['image'] ?? '');
    $stock_quantity = (int)($_POST['stock_quantity'] ?? 0);
    $featured = isset($_POST['featured']) ? 1 : 0;
    
    if (empty($name) || empty($description) || $price <= 0 || empty($category)) {
        $error = 'Please fill in all required fields with valid data';
    } else {
        global $pdo;
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category, image, stock_quantity, featured, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())");
        
        if ($stmt->execute([$name, $description, $price, $category, $image, $stock_quantity, $featured])) {
            flash_message('Product added successfully!', 'success');
            redirect('products.php');
        } else {
            $error = 'Error adding product. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Product - Griffin Gadgets Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="products.php" class="active">Products</a></li>
                    <li><a href="users.php">Users</a></li>
                    <li><a href="orders.php">Orders</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Admin: <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <div class="container mt-3">
        <!-- Page Header -->
        <div class="card mb-3">
            <div class="card-body">
                <h1><i class="fas fa-plus-circle"></i> Add New Product</h1>
                <p class="text-muted">Add a new product to the Griffin Gadgets catalog.</p>
            </div>
        </div>

        <div class="row">
            <div class="col-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-cube"></i> Product Information</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row">
                                <div class="col-8">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Product Name *</label>
                                        <input type="text" id="name" name="name" class="form-control" 
                                               value="<?= htmlspecialchars($_POST['name'] ?? '') ?>" required>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mb-3">
                                        <label for="price" class="form-label">Price (₦) *</label>
                                        <input type="number" id="price" name="price" class="form-control" 
                                               step="0.01" min="0" value="<?= htmlspecialchars($_POST['price'] ?? '') ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description *</label>
                                <textarea id="description" name="description" class="form-control" rows="4" required><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category *</label>
                                        <select id="category" name="category" class="form-control" required>
                                            <option value="">Select Category</option>
                                            <option value="Smartphones" <?= ($_POST['category'] ?? '') === 'Smartphones' ? 'selected' : '' ?>>Smartphones</option>
                                            <option value="Laptops" <?= ($_POST['category'] ?? '') === 'Laptops' ? 'selected' : '' ?>>Laptops</option>
                                            <option value="Tablets" <?= ($_POST['category'] ?? '') === 'Tablets' ? 'selected' : '' ?>>Tablets</option>
                                            <option value="Audio" <?= ($_POST['category'] ?? '') === 'Audio' ? 'selected' : '' ?>>Audio</option>
                                            <option value="Wearables" <?= ($_POST['category'] ?? '') === 'Wearables' ? 'selected' : '' ?>>Wearables</option>
                                            <option value="Accessories" <?= ($_POST['category'] ?? '') === 'Accessories' ? 'selected' : '' ?>>Accessories</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label for="stock_quantity" class="form-label">Stock Quantity</label>
                                        <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" 
                                               min="0" value="<?= htmlspecialchars($_POST['stock_quantity'] ?? '0') ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-8">
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Image Filename</label>
                                        <input type="text" id="image" name="image" class="form-control" 
                                               placeholder="e.g., iphone15pro.jpg" value="<?= htmlspecialchars($_POST['image'] ?? '') ?>">
                                        <small class="form-text text-muted">Upload image to /images/products/ folder first</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mb-3">
                                        <label class="form-label">Options</label>
                                        <div class="form-check">
                                            <input type="checkbox" id="featured" name="featured" class="form-check-input" 
                                                   <?= isset($_POST['featured']) ? 'checked' : '' ?>>
                                            <label for="featured" class="form-check-label">Featured Product</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="products.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Product
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-4">
                <!-- Help Card -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Help</h5>
                    </div>
                    <div class="card-body">
                        <h6>Product Images</h6>
                        <p class="small">Upload product images to the <code>/images/products/</code> folder, then enter just the filename here.</p>
                        
                        <h6>Featured Products</h6>
                        <p class="small">Featured products appear on the homepage and get priority in search results.</p>
                        
                        <h6>Categories</h6>
                        <p class="small">Choose the most appropriate category for better organization and filtering.</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <a href="products.php" class="btn btn-outline-primary btn-sm d-block mb-2">
                            <i class="fas fa-list"></i> View All Products
                        </a>
                        <a href="index.php" class="btn btn-outline-secondary btn-sm d-block">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
