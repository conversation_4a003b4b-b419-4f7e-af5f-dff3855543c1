<?php
require_once '../includes/functions.php';

// Require admin login
require_role('admin');

$user = get_logged_in_user();

// Get all orders with user information
global $pdo;
$stmt = $pdo->prepare("
    SELECT o.*, u.full_name, u.username, u.email,
           COUNT(oi.id) as item_count
    FROM orders o 
    JOIN users u ON o.user_id = u.id 
    LEFT JOIN order_items oi ON o.id = oi.order_id
    GROUP BY o.id
    ORDER BY o.created_at DESC
");
$stmt->execute();
$orders = $stmt->fetchAll();

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management - Griffin Gadgets Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="users.php">Users</a></li>
                    <li><a href="orders.php" class="active">Orders</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Admin: <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Page Header -->
        <div class="card mb-3">
            <div class="card-body">
                <h1><i class="fas fa-shopping-bag"></i> Order Management</h1>
                <p class="text-muted">Monitor and manage customer orders.</p>
            </div>
        </div>

        <!-- Order Statistics -->
        <div class="row mb-3">
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary"><?= count($orders) ?></h4>
                        <p class="mb-0">Total Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning"><?= count(array_filter($orders, fn($o) => $o['status'] === 'pending')) ?></h4>
                        <p class="mb-0">Pending Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info"><?= count(array_filter($orders, fn($o) => $o['status'] === 'processing')) ?></h4>
                        <p class="mb-0">Processing</p>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success"><?= count(array_filter($orders, fn($o) => $o['status'] === 'delivered')) ?></h4>
                        <p class="mb-0">Delivered</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Table -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-list"></i> All Orders</h3>
            </div>
            <div class="card-body">
                <?php if (empty($orders)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-shopping-bag" style="font-size: 4rem; color: #ccc; margin-bottom: 2rem;"></i>
                        <h4>No orders found</h4>
                        <p class="text-muted">Orders will appear here when customers make purchases.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Amount</th>
                                    <th>Payment</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($order['order_number']) ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?= htmlspecialchars($order['full_name'] ?: $order['username']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= htmlspecialchars($order['email']) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <?= date('M j, Y', strtotime($order['created_at'])) ?>
                                                <br>
                                                <small class="text-muted"><?= date('g:i A', strtotime($order['created_at'])) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= $order['item_count'] ?> items</span>
                                        </td>
                                        <td>
                                            <strong><?= format_currency($order['total_amount']) ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <small class="text-muted"><?= ucfirst($order['payment_method']) ?></small>
                                                <?php if ($order['payment_reference']): ?>
                                                    <br>
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle"></i> Paid
                                                    </small>
                                                <?php else: ?>
                                                    <br>
                                                    <small class="text-warning">
                                                        <i class="fas fa-clock"></i> Pending
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= 
                                                $order['status'] === 'delivered' ? 'success' : 
                                                ($order['status'] === 'cancelled' ? 'danger' : 
                                                ($order['status'] === 'processing' ? 'info' : 'warning')) 
                                            ?>">
                                                <?= ucfirst($order['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="order-details.php?id=<?= $order['id'] ?>" class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($order['status'] === 'pending'): ?>
                                                            <li><a class="dropdown-item" href="update-order.php?id=<?= $order['id'] ?>&status=processing">
                                                                <i class="fas fa-play text-info"></i> Mark Processing
                                                            </a></li>
                                                        <?php endif; ?>
                                                        <?php if ($order['status'] === 'processing'): ?>
                                                            <li><a class="dropdown-item" href="update-order.php?id=<?= $order['id'] ?>&status=shipped">
                                                                <i class="fas fa-truck text-primary"></i> Mark Shipped
                                                            </a></li>
                                                            <li><a class="dropdown-item" href="update-order.php?id=<?= $order['id'] ?>&status=delivered">
                                                                <i class="fas fa-check text-success"></i> Mark Delivered
                                                            </a></li>
                                                        <?php endif; ?>
                                                        <?php if ($order['status'] !== 'cancelled' && $order['status'] !== 'delivered'): ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-danger" href="update-order.php?id=<?= $order['id'] ?>&status=cancelled" onclick="return confirm('Cancel this order?')">
                                                                <i class="fas fa-times"></i> Cancel Order
                                                            </a></li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-3">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
