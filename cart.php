<?php
require_once 'includes/functions.php';

// Require login
require_login();

// Handle cart updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_quantity':
                $cart_id = (int)$_POST['cart_id'];
                $quantity = (int)$_POST['quantity'];
                
                if ($quantity > 0) {
                    global $pdo;
                    $stmt = $pdo->prepare("UPDATE cart SET quantity = ? WHERE id = ? AND user_id = ?");
                    $stmt->execute([$quantity, $cart_id, $_SESSION['user_id']]);
                    flash_message('Cart updated successfully!', 'success');
                } else {
                    // Remove item if quantity is 0
                    global $pdo;
                    $stmt = $pdo->prepare("DELETE FROM cart WHERE id = ? AND user_id = ?");
                    $stmt->execute([$cart_id, $_SESSION['user_id']]);
                    flash_message('Item removed from cart!', 'info');
                }
                redirect('cart.php');
                break;
                
            case 'remove_item':
                $cart_id = (int)$_POST['cart_id'];
                global $pdo;
                $stmt = $pdo->prepare("DELETE FROM cart WHERE id = ? AND user_id = ?");
                $stmt->execute([$cart_id, $_SESSION['user_id']]);
                flash_message('Item removed from cart!', 'info');
                redirect('cart.php');
                break;
                
            case 'clear_cart':
                clear_cart();
                flash_message('Cart cleared!', 'info');
                redirect('cart.php');
                break;
        }
    }
}

$cart_items = get_cart_items();
$cart_total = get_cart_total();
$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="cart.php" class="active">Cart</a></li>
                </ul>
                
                <div class="user-menu">
                    <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                    <?php if ($_SESSION['user_role'] === 'admin'): ?>
                        <a href="admin/" class="btn btn-warning">Admin Panel</a>
                    <?php elseif ($_SESSION['user_role'] === 'sales_rep'): ?>
                        <a href="sales_rep/" class="btn btn-warning">Dashboard</a>
                    <?php else: ?>
                        <a href="customer/" class="btn btn-info">My Account</a>
                    <?php endif; ?>
                    <a href="logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <div class="row">
            <div class="col-8">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-shopping-cart"></i> Shopping Cart</h2>
                    </div>
                    <div class="card-body">
                        <?php if (empty($cart_items)): ?>
                            <div class="text-center p-5">
                                <i class="fas fa-shopping-cart" style="font-size: 4rem; color: #ccc; margin-bottom: 2rem;"></i>
                                <h3>Your cart is empty</h3>
                                <p class="text-muted">Add some products to your cart to get started!</p>
                                <a href="products.php" class="btn btn-primary">
                                    <i class="fas fa-shopping-bag"></i> Continue Shopping
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="cart-items">
                                <?php foreach ($cart_items as $item): ?>
                                    <div class="cart-item">
                                        <div class="row align-items-center">
                                            <div class="col-2">
                                                <div class="product-image-small">
                                                    <?php if ($item['image'] && file_exists("images/products/" . $item['image'])): ?>
                                                        <img src="images/products/<?= htmlspecialchars($item['image']) ?>" 
                                                             alt="<?= htmlspecialchars($item['name']) ?>">
                                                    <?php else: ?>
                                                        <i class="fas fa-cube"></i>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <h4><?= htmlspecialchars($item['name']) ?></h4>
                                                <p class="text-muted"><?= format_currency($item['price']) ?> each</p>
                                            </div>
                                            <div class="col-2">
                                                <form method="POST" class="quantity-form">
                                                    <input type="hidden" name="action" value="update_quantity">
                                                    <input type="hidden" name="cart_id" value="<?= $item['id'] ?>">
                                                    <div class="quantity-controls">
                                                        <button type="button" class="btn btn-sm btn-secondary" onclick="changeQuantity(this, -1)">-</button>
                                                        <input type="number" name="quantity" value="<?= $item['quantity'] ?>" 
                                                               min="0" max="99" class="form-control quantity-input">
                                                        <button type="button" class="btn btn-sm btn-secondary" onclick="changeQuantity(this, 1)">+</button>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="col-2 text-center">
                                                <strong><?= format_currency($item['subtotal']) ?></strong>
                                            </div>
                                            <div class="col-2 text-center">
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="remove_item">
                                                    <input type="hidden" name="cart_id" value="<?= $item['id'] ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Remove this item from cart?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="cart-actions mt-3">
                                <div class="row">
                                    <div class="col-6">
                                        <a href="products.php" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left"></i> Continue Shopping
                                        </a>
                                    </div>
                                    <div class="col-6 text-right">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="clear_cart">
                                            <button type="submit" class="btn btn-warning" 
                                                    onclick="return confirm('Clear entire cart?')">
                                                <i class="fas fa-trash-alt"></i> Clear Cart
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if (!empty($cart_items)): ?>
                <div class="col-4">
                    <!-- Order Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-receipt"></i> Order Summary</h3>
                        </div>
                        <div class="card-body">
                            <div class="summary-row">
                                <span>Subtotal (<?= count($cart_items) ?> items):</span>
                                <strong><?= format_currency($cart_total) ?></strong>
                            </div>
                            <div class="summary-row">
                                <span>Shipping:</span>
                                <span><?= $cart_total >= 100000 ? 'FREE' : format_currency(5000) ?></span>
                            </div>
                            <hr>
                            <div class="summary-row total">
                                <span><strong>Total:</strong></span>
                                <strong><?= format_currency($cart_total + ($cart_total >= 100000 ? 0 : 5000)) ?></strong>
                            </div>

                            <?php if ($cart_total < 100000): ?>
                                <div class="shipping-notice">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Add <?= format_currency(100000 - $cart_total) ?> more for free shipping!
                                    </small>
                                </div>
                            <?php else: ?>
                                <div class="shipping-notice">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle"></i>
                                        You qualify for free shipping!
                                    </small>
                                </div>
                            <?php endif; ?>

                            <div class="checkout-actions mt-3">
                                <a href="checkout.php" class="btn btn-success btn-lg" style="width: 100%;">
                                    <i class="fas fa-credit-card"></i> Proceed to Checkout
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Security Notice -->
                    <div class="card mt-3">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt" style="font-size: 2rem; color: #28a745; margin-bottom: 1rem;"></i>
                            <h5>Secure Checkout</h5>
                            <p class="text-muted small">Your payment information is protected with bank-level security.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        function changeQuantity(button, change) {
            const input = button.parentNode.querySelector('.quantity-input');
            const currentValue = parseInt(input.value);
            const newValue = Math.max(0, currentValue + change);
            input.value = newValue;
            
            // Auto-submit form after a short delay
            setTimeout(() => {
                button.closest('form').submit();
            }, 500);
        }

        // Auto-submit quantity forms when input changes
        document.querySelectorAll('.quantity-input').forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.closest('form').submit();
                }, 1000);
            });
        });
    </script>
    <style>
        .cart-item {
            padding: 1.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .product-image-small {
            width: 80px;
            height: 80px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .product-image-small img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .product-image-small i {
            font-size: 2rem;
            color: #ccc;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .quantity-input {
            width: 60px;
            text-align: center;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .summary-row.total {
            font-size: 1.2rem;
            margin-top: 1rem;
        }
        .shipping-notice {
            margin-top: 1rem;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 6px;
            text-align: center;
        }
        .align-items-center {
            align-items: center;
        }
    </style>
</body>
</html>
