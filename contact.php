<?php
require_once 'includes/functions.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $subject = sanitize_input($_POST['subject'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = 'Please fill in all fields';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        // In a real application, you would send an email or save to database
        // For demo purposes, we'll just show a success message
        $success = 'Thank you for your message! We will get back to you within 24 hours.';
        
        // Clear form data on success
        $name = $email = $subject = $message = '';
    }
}

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="about.php">About</a></li>
                    <li><a href="contact.php" class="active">Contact</a></li>
                </ul>
                
                <div class="user-menu">
                    <?php if (is_logged_in()): ?>
                        <a href="cart.php" class="btn btn-info">
                            <i class="fas fa-shopping-cart"></i> Cart (<span id="cart-count"><?= count(get_cart_items()) ?></span>)
                        </a>
                        <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                        <a href="logout.php" class="btn btn-danger">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-primary">Login</a>
                        <a href="register.php" class="btn btn-success">Register</a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container text-center">
            <h1>Contact Griffin Gadgets</h1>
            <p>We're here to help! Get in touch with our friendly team</p>
        </div>
    </section>

    <div class="container mt-3">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-8">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-envelope"></i> Send us a Message</h2>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?= htmlspecialchars($success) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" data-validate>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="name" class="form-label">
                                            <i class="fas fa-user"></i> Full Name *
                                        </label>
                                        <input type="text" id="name" name="name" class="form-control" 
                                               value="<?= htmlspecialchars($name ?? '') ?>" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i> Email Address *
                                        </label>
                                        <input type="email" id="email" name="email" class="form-control" 
                                               value="<?= htmlspecialchars($email ?? '') ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="subject" class="form-label">
                                    <i class="fas fa-tag"></i> Subject *
                                </label>
                                <select id="subject" name="subject" class="form-control" required>
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry" <?= ($subject ?? '') === 'General Inquiry' ? 'selected' : '' ?>>General Inquiry</option>
                                    <option value="Product Question" <?= ($subject ?? '') === 'Product Question' ? 'selected' : '' ?>>Product Question</option>
                                    <option value="Order Support" <?= ($subject ?? '') === 'Order Support' ? 'selected' : '' ?>>Order Support</option>
                                    <option value="Technical Support" <?= ($subject ?? '') === 'Technical Support' ? 'selected' : '' ?>>Technical Support</option>
                                    <option value="Warranty Claim" <?= ($subject ?? '') === 'Warranty Claim' ? 'selected' : '' ?>>Warranty Claim</option>
                                    <option value="Partnership" <?= ($subject ?? '') === 'Partnership' ? 'selected' : '' ?>>Partnership Opportunity</option>
                                    <option value="Feedback" <?= ($subject ?? '') === 'Feedback' ? 'selected' : '' ?>>Feedback & Suggestions</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="message" class="form-label">
                                    <i class="fas fa-comment"></i> Message *
                                </label>
                                <textarea id="message" name="message" class="form-control" rows="6" 
                                          placeholder="Please provide as much detail as possible..." required><?= htmlspecialchars($message ?? '') ?></textarea>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane"></i> Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-4">
                <!-- Contact Details -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> Contact Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Address</h4>
                                <p>123 Tech Street<br>Victoria Island, Lagos<br>Nigeria</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Phone</h4>
                                <p>+234 800 GRIFFIN<br>+234 1 234 5678</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Email</h4>
                                <p><EMAIL><br><EMAIL></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Business Hours</h4>
                                <p>Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 4:00 PM<br>Sunday: Closed</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Support -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3><i class="fas fa-headset"></i> Quick Support</h3>
                    </div>
                    <div class="card-body">
                        <div class="support-options">
                            <a href="tel:+2348000474334" class="support-option">
                                <i class="fas fa-phone"></i>
                                <div>
                                    <h4>Call Us</h4>
                                    <p>Speak with our support team</p>
                                </div>
                            </a>

                            <a href="mailto:<EMAIL>" class="support-option">
                                <i class="fas fa-envelope"></i>
                                <div>
                                    <h4>Email Support</h4>
                                    <p>Get help via email</p>
                                </div>
                            </a>

                            <?php if (is_logged_in()): ?>
                                <a href="customer/" class="support-option">
                                    <i class="fas fa-user"></i>
                                    <div>
                                        <h4>My Account</h4>
                                        <p>Check your orders & profile</p>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3><i class="fas fa-share-alt"></i> Follow Us</h3>
                    </div>
                    <div class="card-body text-center">
                        <div class="social-links">
                            <a href="#" class="social-link facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link linkedin">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="social-link youtube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                        <p class="text-muted mt-2">Stay updated with our latest products and offers</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="card mt-3">
            <div class="card-header">
                <h2><i class="fas fa-question-circle"></i> Frequently Asked Questions</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="faq-item">
                            <h4>How can I track my order?</h4>
                            <p>Once your order ships, you'll receive a tracking number via email. You can also check your order status in your account dashboard.</p>
                        </div>
                        <div class="faq-item">
                            <h4>What is your return policy?</h4>
                            <p>We offer a 30-day return policy for unused items in original packaging. Contact our support team to initiate a return.</p>
                        </div>
                        <div class="faq-item">
                            <h4>Do you offer warranty on products?</h4>
                            <p>Yes, all our products come with manufacturer warranty. Extended warranty options are also available for select items.</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="faq-item">
                            <h4>What payment methods do you accept?</h4>
                            <p>We accept all major credit/debit cards, bank transfers, and mobile money payments through our secure Paystack integration.</p>
                        </div>
                        <div class="faq-item">
                            <h4>How long does delivery take?</h4>
                            <p>Delivery typically takes 2-5 business days within Lagos and 3-7 business days for other states across Nigeria.</p>
                        </div>
                        <div class="faq-item">
                            <h4>Are your products authentic?</h4>
                            <p>Absolutely! We source all products directly from authorized distributors and manufacturers to guarantee authenticity.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Griffin Gadgets</h3>
                    <p>Your trusted partner for premium electronics and cutting-edge technology.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <a href="products.php">Products</a>
                    <a href="about.php">About Us</a>
                    <a href="contact.php">Contact</a>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-phone"></i> +234 800 GRIFFIN</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Griffin Gadgets. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <style>
        .contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 2rem;
            gap: 1rem;
        }
        .contact-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }
        .contact-details h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }
        .contact-details p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        .support-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .support-option {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        .support-option:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
        }
        .support-option i {
            font-size: 1.5rem;
            color: #667eea;
        }
        .support-option h4 {
            margin: 0 0 0.25rem 0;
        }
        .support-option p {
            margin: 0;
            font-size: 0.875rem;
            color: #666;
        }
        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        .social-link {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }
        .social-link:hover {
            transform: translateY(-3px);
        }
        .social-link.facebook { background: #3b5998; }
        .social-link.twitter { background: #1da1f2; }
        .social-link.instagram { background: #e4405f; }
        .social-link.linkedin { background: #0077b5; }
        .social-link.youtube { background: #ff0000; }
        .faq-item {
            margin-bottom: 2rem;
        }
        .faq-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .faq-item p {
            color: #666;
            line-height: 1.6;
        }
    </style>
</body>
</html>
