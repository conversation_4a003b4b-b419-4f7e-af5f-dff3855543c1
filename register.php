<?php
require_once 'includes/functions.php';

// Redirect if already logged in
if (is_logged_in()) {
    redirect('customer/');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $full_name = sanitize_input($_POST['full_name'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $role = sanitize_input($_POST['role'] ?? 'customer');
    $security_question1 = sanitize_input($_POST['security_question1'] ?? '');
    $security_answer1 = sanitize_input($_POST['security_answer1'] ?? '');
    $security_question2 = sanitize_input($_POST['security_question2'] ?? '');
    $security_answer2 = sanitize_input($_POST['security_answer2'] ?? '');

    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
        $error = 'Please fill in all required fields';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } elseif (!empty($phone) && (!preg_match('/^\d{11}$/', $phone))) {
        $error = 'Phone number must be exactly 11 digits';
    } elseif (empty($security_question1) || empty($security_answer1) || empty($security_question2) || empty($security_answer2)) {
        $error = 'Both security questions and answers are required';
    } elseif ($security_question1 === $security_question2) {
        $error = 'Please select different security questions';
    } else {
        // Check if username or email already exists
        global $pdo;
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetchColumn() > 0) {
            $error = 'Username or email already exists';
        } else {
            // Create user
            if (create_user($username, $email, $password, $role, $full_name, $phone)) {
                // Update security questions (now mandatory)
                $stmt = $pdo->prepare("UPDATE users SET security_question = ?, security_answer = ?, security_question2 = ?, security_answer2 = ? WHERE username = ?");
                $stmt->execute([
                    $security_question1,
                    password_hash($security_answer1, PASSWORD_DEFAULT),
                    $security_question2,
                    password_hash($security_answer2, PASSWORD_DEFAULT),
                    $username
                ]);
                
                if ($role === 'customer') {
                    // Auto-login for customers
                    authenticate_user($username, $password);
                    flash_message('Account created successfully! Welcome to Griffin Gadgets!', 'success');
                    redirect('customer/');
                } else {
                    $success = 'Account created successfully! Your sales representative account is pending approval. You will be notified once approved.';
                }
            } else {
                $error = 'Error creating account. Please try again.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Griffin Gadgets</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 0;
        }
        .auth-card {
            background: white;
            padding: 2.5rem;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 900px; /* Increased for horizontal layout */
            margin: 1rem;
        }
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .auth-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .auth-header p {
            color: #666;
        }
        .auth-links {
            text-align: center;
            margin-top: 2rem;
        }
        .auth-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
        }
        .auth-links a:hover {
            text-decoration: underline;
        }
        .role-selection {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .role-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .role-option.active {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .role-option input[type="radio"] {
            display: none;
        }
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .password-strength.weak { color: #dc3545; }
        .password-strength.fair { color: #ffc107; }
        .password-strength.good { color: #28a745; }
        .password-strength.strong { color: #007bff; }

        /* Password toggle styles */
        .password-field {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
            z-index: 10;
        }
        .password-toggle:hover {
            color: #333;
        }

        /* Enhanced form layout */
        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .form-col {
            flex: 1;
        }
        .form-col-2 {
            flex: 2;
        }

        /* Security questions styling */
        .security-section {
            background: #f8f9ff;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        .security-section h4 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        /* Mobile number styling */
        .phone-input {
            position: relative;
        }
        .phone-prefix {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: 600;
            z-index: 5;
        }
        .phone-input input {
            padding-left: 45px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .auth-card {
                max-width: 95%;
                padding: 1.5rem;
                margin: 0.5rem;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .role-selection {
                flex-direction: column;
                gap: 0.5rem;
            }

            .security-section {
                padding: 1rem;
            }

            .auth-header h1 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .auth-container {
                padding: 1rem 0;
            }

            .auth-card {
                padding: 1rem;
            }

            .phone-prefix {
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1><i class="fas fa-bolt"></i> Griffin Gadgets</h1>
                <p>Create your account</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <form method="POST" data-validate>
                <!-- Account Type Selection -->
                <div class="form-group">
                    <label class="form-label">Account Type</label>
                    <div class="role-selection">
                        <div class="role-option active" onclick="selectRole('customer')">
                            <input type="radio" name="role" value="customer" checked>
                            <i class="fas fa-user"></i>
                            <div>Customer</div>
                            <small>Shop and buy products</small>
                        </div>
                        <div class="role-option" onclick="selectRole('sales_rep')">
                            <input type="radio" name="role" value="sales_rep">
                            <i class="fas fa-briefcase"></i>
                            <div>Sales Rep</div>
                            <small>Manage orders & invoices</small>
                        </div>
                    </div>
                </div>

                <!-- Basic Information Row -->
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="username" class="form-label">
                                <i class="fas fa-user"></i> Username *
                            </label>
                            <input type="text" id="username" name="username" class="form-control"
                                   value="<?= htmlspecialchars($username ?? '') ?>" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i> Email *
                            </label>
                            <input type="email" id="email" name="email" class="form-control"
                                   value="<?= htmlspecialchars($email ?? '') ?>" required>
                        </div>
                    </div>
                </div>

                <!-- Name and Phone Row -->
                <div class="form-row">
                    <div class="form-col-2">
                        <div class="form-group">
                            <label for="full_name" class="form-label">
                                <i class="fas fa-id-card"></i> Full Name *
                            </label>
                            <input type="text" id="full_name" name="full_name" class="form-control"
                                   value="<?= htmlspecialchars($full_name ?? '') ?>" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone"></i> Phone Number (11 digits)
                            </label>
                            <div class="phone-input">
                                <span class="phone-prefix">+234</span>
                                <input type="tel" id="phone" name="phone" class="form-control"
                                       value="<?= htmlspecialchars($phone ?? '') ?>"
                                       maxlength="11" pattern="[0-9]{11}"
                                       placeholder="08012345678"
                                       title="Please enter exactly 11 digits">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Password Row -->
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock"></i> Password *
                            </label>
                            <div class="password-field">
                                <input type="password" id="password" name="password" class="form-control"
                                       data-strength required>
                                <i class="fas fa-eye password-toggle" onclick="togglePassword('password')"></i>
                            </div>
                            <div id="password-strength" class="password-strength"></div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock"></i> Confirm Password *
                            </label>
                            <div class="password-field">
                                <input type="password" id="confirm_password" name="confirm_password"
                                       class="form-control" data-confirm-password="password" required>
                                <i class="fas fa-eye password-toggle" onclick="togglePassword('confirm_password')"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Questions Section -->
                <div class="security-section">
                    <h4><i class="fas fa-shield-alt"></i> Security Questions (Required for Password Recovery)</h4>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="security_question1" class="form-label">
                                    <i class="fas fa-question-circle"></i> Security Question 1 *
                                </label>
                                <select id="security_question1" name="security_question1" class="form-control" required>
                                    <option value="">Select first security question</option>
                                    <option value="What was your first pet's name?">What was your first pet's name?</option>
                                    <option value="What city were you born in?">What city were you born in?</option>
                                    <option value="What was your mother's maiden name?">What was your mother's maiden name?</option>
                                    <option value="What was the name of your first school?">What was the name of your first school?</option>
                                    <option value="What is your favorite food?">What is your favorite food?</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="security_answer1" class="form-label">
                                    <i class="fas fa-key"></i> Answer 1 *
                                </label>
                                <input type="text" id="security_answer1" name="security_answer1" class="form-control"
                                       placeholder="Enter your answer" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="security_question2" class="form-label">
                                    <i class="fas fa-question-circle"></i> Security Question 2 *
                                </label>
                                <select id="security_question2" name="security_question2" class="form-control" required>
                                    <option value="">Select second security question</option>
                                    <option value="What was your childhood nickname?">What was your childhood nickname?</option>
                                    <option value="What is the name of your best friend?">What is the name of your best friend?</option>
                                    <option value="What was your first car model?">What was your first car model?</option>
                                    <option value="What is your favorite movie?">What is your favorite movie?</option>
                                    <option value="What street did you grow up on?">What street did you grow up on?</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="security_answer2" class="form-label">
                                    <i class="fas fa-key"></i> Answer 2 *
                                </label>
                                <input type="text" id="security_answer2" name="security_answer2" class="form-control"
                                       placeholder="Enter your answer" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-user-plus"></i> Create Account
                    </button>
                </div>
            </form>

            <div class="auth-links">
                <a href="login.php">Already have an account? Sign In</a>
            </div>

            <div class="auth-links mt-2">
                <a href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Home
                </a>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        function selectRole(role) {
            // Remove active class from all options
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('active');
            });

            // Add active class to selected option
            event.currentTarget.classList.add('active');

            // Check the radio button
            document.querySelector(`input[value="${role}"]`).checked = true;
        }

        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const toggle = field.nextElementSibling;

            if (field.type === 'password') {
                field.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }

        // Phone number validation
        document.getElementById('phone').addEventListener('input', function(e) {
            // Remove any non-digit characters
            this.value = this.value.replace(/\D/g, '');

            // Limit to 11 digits
            if (this.value.length > 11) {
                this.value = this.value.slice(0, 11);
            }
        });

        // Security question validation
        document.getElementById('security_question1').addEventListener('change', function() {
            validateSecurityQuestions();
        });

        document.getElementById('security_question2').addEventListener('change', function() {
            validateSecurityQuestions();
        });

        function validateSecurityQuestions() {
            const q1 = document.getElementById('security_question1').value;
            const q2 = document.getElementById('security_question2').value;

            if (q1 && q2 && q1 === q2) {
                alert('Please select different security questions');
                document.getElementById('security_question2').value = '';
            }
        }

        // Form validation on submit
        document.querySelector('form').addEventListener('submit', function(e) {
            const phone = document.getElementById('phone').value;
            const q1 = document.getElementById('security_question1').value;
            const q2 = document.getElementById('security_question2').value;

            if (phone && phone.length !== 11) {
                e.preventDefault();
                alert('Phone number must be exactly 11 digits');
                return false;
            }

            if (q1 === q2) {
                e.preventDefault();
                alert('Please select different security questions');
                return false;
            }
        });
    </script>
</body>
</html>
