<?php
require_once '../includes/functions.php';

// Require customer login
require_role('customer');

$user = get_logged_in_user();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = sanitize_input($_POST['full_name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    
    if (empty($full_name) || empty($email)) {
        $error = 'Please fill in all required fields';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        // Check if email is already taken by another user
        global $pdo;
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $_SESSION['user_id']]);
        
        if ($stmt->fetchColumn() > 0) {
            $error = 'Email address is already in use by another account';
        } else {
            // Update user profile
            $stmt = $pdo->prepare("UPDATE users SET full_name = ?, email = ?, phone = ?, updated_at = NOW() WHERE id = ?");
            if ($stmt->execute([$full_name, $email, $phone, $_SESSION['user_id']])) {
                $success = 'Profile updated successfully!';
                // Refresh user data
                $user = get_logged_in_user();
            } else {
                $error = 'Error updating profile. Please try again.';
            }
        }
    }
}

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Griffin Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="../products.php">Products</a></li>
                    <li><a href="index.php">My Account</a></li>
                </ul>
                
                <div class="user-menu">
                    <a href="../cart.php" class="btn btn-info">
                        <i class="fas fa-shopping-cart"></i> Cart (<?= count(get_cart_items()) ?>)
                    </a>
                    <span>Welcome, <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-3">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-user"></i> My Account</h3>
                    </div>
                    <div class="card-body">
                        <nav class="account-nav">
                            <a href="index.php" class="nav-item">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                            <a href="profile.php" class="nav-item active">
                                <i class="fas fa-user-edit"></i> Profile
                            </a>
                            <a href="orders.php" class="nav-item">
                                <i class="fas fa-shopping-bag"></i> My Orders
                            </a>
                            <a href="../cart.php" class="nav-item">
                                <i class="fas fa-shopping-cart"></i> Shopping Cart
                            </a>
                            <a href="change-password.php" class="nav-item">
                                <i class="fas fa-key"></i> Change Password
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-9">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-user-edit"></i> Edit Profile</h2>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?= htmlspecialchars($success) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" id="username" class="form-control" 
                                               value="<?= htmlspecialchars($user['username']) ?>" readonly>
                                        <small class="form-text text-muted">Username cannot be changed</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="role" class="form-label">Account Type</label>
                                        <input type="text" id="role" class="form-control" 
                                               value="<?= ucfirst(str_replace('_', ' ', $user['role'])) ?>" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="full_name" class="form-label">
                                    <i class="fas fa-user"></i> Full Name *
                                </label>
                                <input type="text" id="full_name" name="full_name" class="form-control" 
                                       value="<?= htmlspecialchars($user['full_name']) ?>" required>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i> Email Address *
                                        </label>
                                        <input type="email" id="email" name="email" class="form-control" 
                                               value="<?= htmlspecialchars($user['email']) ?>" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="fas fa-phone"></i> Phone Number
                                        </label>
                                        <input type="tel" id="phone" name="phone" class="form-control" 
                                               value="<?= htmlspecialchars($user['phone']) ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="status" class="form-label">Account Status</label>
                                        <input type="text" id="status" class="form-control" 
                                               value="<?= ucfirst($user['status']) ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="created_at" class="form-label">Member Since</label>
                                        <input type="text" id="created_at" class="form-control" 
                                               value="<?= date('F j, Y', strtotime($user['created_at'])) ?>" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Update Profile
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Account Information -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> Account Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h5>Security</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-shield-alt text-success"></i> Account is secure</li>
                                    <li><i class="fas fa-key text-info"></i> <a href="change-password.php">Change Password</a></li>
                                    <?php if ($user['security_question']): ?>
                                        <li><i class="fas fa-question-circle text-warning"></i> Security question set</li>
                                    <?php else: ?>
                                        <li><i class="fas fa-exclamation-triangle text-warning"></i> No security question set</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <div class="col-6">
                                <h5>Account Activity</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-calendar text-info"></i> Joined: <?= date('F j, Y', strtotime($user['created_at'])) ?></li>
                                    <?php if ($user['updated_at']): ?>
                                        <li><i class="fas fa-edit text-success"></i> Last updated: <?= date('F j, Y', strtotime($user['updated_at'])) ?></li>
                                    <?php endif; ?>
                                    <li><i class="fas fa-user-check text-success"></i> Status: <?= ucfirst($user['status']) ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/main.js"></script>
    <style>
        .account-nav {
            display: flex;
            flex-direction: column;
        }
        .nav-item {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        .nav-item:hover,
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .form-actions {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #dee2e6;
        }
    </style>
</body>
</html>
