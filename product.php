<?php
require_once 'includes/functions.php';

$product_id = (int)($_GET['id'] ?? 0);

if ($product_id <= 0) {
    flash_message('Product not found', 'danger');
    redirect('products.php');
}

$product = get_product($product_id);

if (!$product) {
    flash_message('Product not found', 'danger');
    redirect('products.php');
}

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($product['name']) ?> - Griffin Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="products.php">Products</a></li>
                    <li><a href="about.php">About</a></li>
                    <li><a href="contact.php">Contact</a></li>
                </ul>
                
                <div class="user-menu">
                    <?php if (is_logged_in()): ?>
                        <a href="cart.php" class="btn btn-info">
                            <i class="fas fa-shopping-cart"></i> Cart (<span id="cart-count"><?= count(get_cart_items()) ?></span>)
                        </a>
                        <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                        <a href="logout.php" class="btn btn-danger">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-primary">Login</a>
                        <a href="register.php" class="btn btn-success">Register</a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item"><a href="products.php">Products</a></li>
                <li class="breadcrumb-item"><a href="products.php?category=<?= urlencode($product['category']) ?>"><?= htmlspecialchars($product['category']) ?></a></li>
                <li class="breadcrumb-item active"><?= htmlspecialchars($product['name']) ?></li>
            </ol>
        </nav>

        <div class="row">
            <!-- Product Image -->
            <div class="col-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="product-image-large">
                            <?php if ($product['image'] && file_exists("images/products/" . $product['image'])): ?>
                                <img src="images/products/<?= htmlspecialchars($product['image']) ?>" 
                                     alt="<?= htmlspecialchars($product['name']) ?>" 
                                     class="img-fluid rounded shadow">
                            <?php else: ?>
                                <div class="placeholder-image">
                                    <i class="fas fa-mobile-alt"></i>
                                    <p>Image not available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($product['featured']): ?>
                            <div class="featured-badge">
                                <i class="fas fa-star"></i> Featured Product
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Product Details -->
            <div class="col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="product-category">
                            <span class="badge bg-info"><?= htmlspecialchars($product['category']) ?></span>
                        </div>
                        
                        <h1 class="product-title"><?= htmlspecialchars($product['name']) ?></h1>
                        
                        <div class="product-price">
                            <?= format_currency($product['price']) ?>
                        </div>
                        
                        <div class="product-description">
                            <p><?= nl2br(htmlspecialchars($product['description'])) ?></p>
                        </div>
                        
                        <div class="product-stock">
                            <?php if ($product['stock_quantity'] > 0): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i> In Stock (<?= $product['stock_quantity'] ?> available)
                                </span>
                            <?php else: ?>
                                <span class="badge bg-danger">
                                    <i class="fas fa-times"></i> Out of Stock
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-actions mt-4">
                            <?php if (is_logged_in()): ?>
                                <?php if ($product['stock_quantity'] > 0): ?>
                                    <div class="quantity-selector mb-3">
                                        <label for="quantity" class="form-label">Quantity:</label>
                                        <div class="input-group" style="width: 150px;">
                                            <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(-1)">-</button>
                                            <input type="number" id="quantity" class="form-control text-center" value="1" min="1" max="<?= $product['stock_quantity'] ?>">
                                            <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(1)">+</button>
                                        </div>
                                    </div>
                                    
                                    <button class="btn btn-success btn-lg add-to-cart" data-product-id="<?= $product['id'] ?>">
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    </button>
                                    
                                    <a href="cart.php" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-shopping-cart"></i> View Cart
                                    </a>
                                <?php else: ?>
                                    <button class="btn btn-secondary btn-lg" disabled>
                                        <i class="fas fa-times"></i> Out of Stock
                                    </button>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="login-prompt">
                                    <p class="text-muted">Please login to purchase this product</p>
                                    <a href="login.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt"></i> Login to Buy
                                    </a>
                                    <a href="register.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-user-plus"></i> Create Account
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-features mt-4">
                            <h5>Product Features:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-shield-alt text-success"></i> Manufacturer Warranty</li>
                                <li><i class="fas fa-truck text-info"></i> Free Shipping on orders over ₦100,000</li>
                                <li><i class="fas fa-undo text-warning"></i> 30-day Return Policy</li>
                                <li><i class="fas fa-headset text-primary"></i> 24/7 Customer Support</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Products -->
        <div class="card mt-4">
            <div class="card-header">
                <h3><i class="fas fa-th"></i> Related Products</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                    $related_products = get_products($product['category'], 4);
                    $related_products = array_filter($related_products, fn($p) => $p['id'] != $product['id']);
                    $related_products = array_slice($related_products, 0, 4);
                    ?>
                    
                    <?php if (empty($related_products)): ?>
                        <div class="col-12 text-center">
                            <p class="text-muted">No related products found.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($related_products as $related): ?>
                            <div class="col-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <div class="product-image-small mb-2">
                                            <?php if ($related['image'] && file_exists("images/products/" . $related['image'])): ?>
                                                <img src="images/products/<?= htmlspecialchars($related['image']) ?>" 
                                                     alt="<?= htmlspecialchars($related['name']) ?>" 
                                                     class="img-fluid">
                                            <?php else: ?>
                                                <i class="fas fa-cube"></i>
                                            <?php endif; ?>
                                        </div>
                                        <h6><?= htmlspecialchars($related['name']) ?></h6>
                                        <p class="text-success"><?= format_currency($related['price']) ?></p>
                                        <a href="product.php?id=<?= $related['id'] ?>" class="btn btn-sm btn-outline-primary">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Back to Products -->
        <div class="text-center mt-3">
            <a href="products.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Griffin Gadgets</h3>
                    <p>Your trusted partner for premium electronics and cutting-edge technology.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <a href="products.php">Products</a>
                    <a href="about.php">About Us</a>
                    <a href="contact.php">Contact</a>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-phone"></i> +234 800 GRIFFIN</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Griffin Gadgets. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        function changeQuantity(change) {
            const quantityInput = document.getElementById('quantity');
            const currentValue = parseInt(quantityInput.value);
            const newValue = currentValue + change;
            const max = parseInt(quantityInput.max);
            
            if (newValue >= 1 && newValue <= max) {
                quantityInput.value = newValue;
            }
        }

        // Update add to cart functionality to use quantity
        document.addEventListener('DOMContentLoaded', function() {
            const addToCartButton = document.querySelector('.add-to-cart');
            if (addToCartButton) {
                addToCartButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.dataset.productId;
                    const quantity = document.getElementById('quantity').value;
                    
                    fetch('ajax/add_to_cart.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: parseInt(quantity)
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Product added to cart!');
                            updateCartCount();
                        } else {
                            alert(data.message || 'Error adding product to cart');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error adding product to cart');
                    });
                });
            }
        });

        function updateCartCount() {
            fetch('ajax/get_cart_count.php')
            .then(response => response.json())
            .then(data => {
                const cartCountElement = document.getElementById('cart-count');
                if (cartCountElement) {
                    cartCountElement.textContent = data.count || 0;
                }
            });
        }
    </script>
    <style>
        .product-image-large {
            max-width: 100%;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .product-image-large img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .placeholder-image {
            text-align: center;
            color: #ccc;
        }
        .placeholder-image i {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .product-title {
            font-size: 2rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        .product-price {
            font-size: 2.5rem;
            font-weight: bold;
            color: #28a745;
            margin: 1rem 0;
        }
        .product-description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin: 1.5rem 0;
        }
        .featured-badge {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            margin-top: 1rem;
            font-weight: 600;
        }
        .product-image-small {
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .product-image-small img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .product-image-small i {
            font-size: 2rem;
            color: #ccc;
        }
    </style>
</body>
</html>
