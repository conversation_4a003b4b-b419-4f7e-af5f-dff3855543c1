// Griffin Gadgets - Main JavaScript Functions

// DOM Ready
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    // Initialize cart functionality
    initializeCart();
    
    // Initialize forms
    initializeForms();
    
    // Initialize alerts
    initializeAlerts();
    
    // Initialize mobile menu
    initializeMobileMenu();
}

// Cart Functions
function initializeCart() {
    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.dataset.productId;
            const quantity = this.dataset.quantity || 1;
            addToCart(productId, quantity);
        });
    });

    // Update cart quantity
    const quantityInputs = document.querySelectorAll('.cart-quantity');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const cartId = this.dataset.cartId;
            const quantity = this.value;
            updateCartQuantity(cartId, quantity);
        });
    });

    // Remove from cart
    const removeButtons = document.querySelectorAll('.remove-from-cart');
    removeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const cartId = this.dataset.cartId;
            removeFromCart(cartId);
        });
    });
}

// Add product to cart
function addToCart(productId, quantity = 1) {
    fetch('ajax/add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Product added to cart!', 'success');
            updateCartCount();
        } else {
            showAlert(data.message || 'Error adding product to cart', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error adding product to cart', 'danger');
    });
}

// Update cart quantity
function updateCartQuantity(cartId, quantity) {
    fetch('ajax/update_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cart_id: cartId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Refresh to show updated totals
        } else {
            showAlert(data.message || 'Error updating cart', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating cart', 'danger');
    });
}

// Remove item from cart
function removeFromCart(cartId) {
    if (confirm('Are you sure you want to remove this item from your cart?')) {
        fetch('ajax/remove_from_cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                cart_id: cartId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                showAlert(data.message || 'Error removing item from cart', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error removing item from cart', 'danger');
        });
    }
}

// Update cart count in header
function updateCartCount() {
    fetch('ajax/get_cart_count.php')
    .then(response => response.json())
    .then(data => {
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = data.count || 0;
        }
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

// Form Functions
function initializeForms() {
    // Form validation
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });

    // Password strength indicator
    const passwordInputs = document.querySelectorAll('input[type="password"][data-strength]');
    passwordInputs.forEach(input => {
        input.addEventListener('input', function() {
            checkPasswordStrength(this);
        });
    });

    // Confirm password validation
    const confirmPasswordInputs = document.querySelectorAll('input[data-confirm-password]');
    confirmPasswordInputs.forEach(input => {
        input.addEventListener('input', function() {
            const passwordInput = document.getElementById(this.dataset.confirmPassword);
            if (passwordInput) {
                validatePasswordMatch(passwordInput, this);
            }
        });
    });
}

// Validate form
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });

    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
    });

    return isValid;
}

// Show field error
function showFieldError(field, message) {
    clearFieldError(field);
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

// Clear field error
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Check password strength
function checkPasswordStrength(input) {
    const password = input.value;
    const strengthIndicator = document.getElementById(input.id + '-strength');
    
    if (!strengthIndicator) return;

    let strength = 0;
    let feedback = '';

    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    switch (strength) {
        case 0:
        case 1:
            feedback = 'Very Weak';
            strengthIndicator.className = 'password-strength weak';
            break;
        case 2:
            feedback = 'Weak';
            strengthIndicator.className = 'password-strength weak';
            break;
        case 3:
            feedback = 'Fair';
            strengthIndicator.className = 'password-strength fair';
            break;
        case 4:
            feedback = 'Good';
            strengthIndicator.className = 'password-strength good';
            break;
        case 5:
            feedback = 'Strong';
            strengthIndicator.className = 'password-strength strong';
            break;
    }

    strengthIndicator.textContent = feedback;
}

// Validate password match
function validatePasswordMatch(passwordInput, confirmInput) {
    if (passwordInput.value !== confirmInput.value) {
        showFieldError(confirmInput, 'Passwords do not match');
    } else {
        clearFieldError(confirmInput);
    }
}

// Alert Functions
function initializeAlerts() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            fadeOut(alert);
        }, 5000);
    });

    // Close button functionality
    const closeButtons = document.querySelectorAll('.alert .close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const alert = this.closest('.alert');
            fadeOut(alert);
        });
    });
}

// Show alert with modern toast design
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();

    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    // Get appropriate icon for the type
    const icons = {
        success: 'fas fa-check-circle',
        danger: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    toast.innerHTML = `
        <div class="toast-content">
            <div class="toast-icon">
                <i class="${icons[type] || icons.info}"></i>
            </div>
            <div class="toast-message">${message}</div>
            <button type="button" class="toast-close" aria-label="Close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="toast-progress"></div>
    `;

    alertContainer.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Add close functionality
    const closeButton = toast.querySelector('.toast-close');
    closeButton.addEventListener('click', function() {
        hideToast(toast);
    });

    // Auto-hide after 4 seconds with progress bar
    const progressBar = toast.querySelector('.toast-progress');
    progressBar.style.animation = 'toast-progress 4s linear forwards';

    setTimeout(() => {
        hideToast(toast);
    }, 4000);
}

// Create alert container if it doesn't exist
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        width: 100%;
    `;

    // Add toast styles to document head if not already added
    if (!document.getElementById('toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            .toast-notification {
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                margin-bottom: 12px;
                transform: translateX(100%);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                opacity: 0;
                overflow: hidden;
                position: relative;
            }

            .toast-notification.show {
                transform: translateX(0);
                opacity: 1;
            }

            .toast-content {
                display: flex;
                align-items: center;
                padding: 16px 20px;
                gap: 12px;
            }

            .toast-icon {
                flex-shrink: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
            }

            .toast-message {
                flex: 1;
                font-weight: 500;
                font-size: 14px;
                line-height: 1.4;
            }

            .toast-close {
                background: none;
                border: none;
                padding: 4px;
                cursor: pointer;
                opacity: 0.6;
                transition: opacity 0.2s;
                border-radius: 4px;
            }

            .toast-close:hover {
                opacity: 1;
                background: rgba(0,0,0,0.05);
            }

            .toast-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: currentColor;
                opacity: 0.3;
                width: 0;
            }

            @keyframes toast-progress {
                from { width: 100%; }
                to { width: 0; }
            }

            .toast-success {
                border-left: 4px solid #10b981;
                color: #065f46;
            }
            .toast-success .toast-icon { color: #10b981; }
            .toast-success .toast-progress { background: #10b981; }

            .toast-danger {
                border-left: 4px solid #ef4444;
                color: #991b1b;
            }
            .toast-danger .toast-icon { color: #ef4444; }
            .toast-danger .toast-progress { background: #ef4444; }

            .toast-warning {
                border-left: 4px solid #f59e0b;
                color: #92400e;
            }
            .toast-warning .toast-icon { color: #f59e0b; }
            .toast-warning .toast-progress { background: #f59e0b; }

            .toast-info {
                border-left: 4px solid #3b82f6;
                color: #1e40af;
            }
            .toast-info .toast-icon { color: #3b82f6; }
            .toast-info .toast-progress { background: #3b82f6; }

            @media (max-width: 480px) {
                #alert-container {
                    left: 12px;
                    right: 12px;
                    top: 12px;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(container);
    return container;
}

// Hide toast notification
function hideToast(toast) {
    toast.classList.remove('show');
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

// Fade out element
function fadeOut(element) {
    element.style.opacity = '0';
    element.style.transition = 'opacity 0.3s ease';
    setTimeout(() => {
        if (element.parentNode) {
            element.parentNode.removeChild(element);
        }
    }, 300);
}

// Mobile Menu
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('show');
        });
    }
}

// Utility Functions
function formatCurrency(amount) {
    return '₦' + parseFloat(amount).toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        const debouncedSearch = debounce(performSearch, 300);
        searchInput.addEventListener('input', function() {
            debouncedSearch(this.value);
        });
    }
}

function performSearch(query) {
    if (query.length < 2) return;
    
    fetch(`ajax/search.php?q=${encodeURIComponent(query)}`)
    .then(response => response.json())
    .then(data => {
        displaySearchResults(data.results);
    })
    .catch(error => {
        console.error('Search error:', error);
    });
}

function displaySearchResults(results) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) return;
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<p>No products found</p>';
        return;
    }
    
    const html = results.map(product => `
        <div class="search-result-item">
            <a href="product.php?id=${product.id}">
                <img src="images/products/${product.image}" alt="${product.name}" class="search-result-image">
                <div class="search-result-info">
                    <h4>${product.name}</h4>
                    <p class="price">${formatCurrency(product.price)}</p>
                </div>
            </a>
        </div>
    `).join('');
    
    resultsContainer.innerHTML = html;
}
