<?php
require_once 'includes/db_connect.php';

echo "<h2>Griffin Gadgets Database Setup</h2>";

try {
    // Create users table
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHA<PERSON>(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('customer', 'sales_rep', 'admin') DEFAULT 'customer',
        full_name VARCHAR(100),
        phone VARCHAR(20),
        status ENUM('active', 'pending', 'suspended') DEFAULT 'active',
        security_question VARCHAR(255),
        security_answer VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✓ Users table created</p>";

    // Create products table
    $pdo->exec("CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHA<PERSON>(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        category VARCHAR(100),
        image VARCHAR(255),
        stock_quantity INT DEFAULT 0,
        featured BOOLEAN DEFAULT FALSE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✓ Products table created</p>";

    // Create cart table
    $pdo->exec("CREATE TABLE IF NOT EXISTS cart (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_product (user_id, product_id)
    )");
    echo "<p>✓ Cart table created</p>";

    // Create orders table
    $pdo->exec("CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50),
        payment_reference VARCHAR(100),
        status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
        shipping_address TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
    echo "<p>✓ Orders table created</p>";

    // Create order_items table
    $pdo->exec("CREATE TABLE IF NOT EXISTS order_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )");
    echo "<p>✓ Order items table created</p>";

    // Create admin user if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    $stmt->execute();
    $admin_count = $stmt->fetchColumn();

    if ($admin_count == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role, full_name, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $admin_password, 'admin', 'System Administrator', 'active']);
        echo "<p>✓ Admin user created (username: admin, password: admin123)</p>";
    }

    // Insert sample products
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products");
    $stmt->execute();
    $product_count = $stmt->fetchColumn();

    if ($product_count == 0) {
        $sample_products = [
            ['iPhone 13 Pro', 'iPhone 13 Pro with ProRes video recording and ProMotion display', 1700000, 'Smartphones', '13pro.jpg', 25, 1],
            ['iPhone 14 Pro', 'iPhone 14 Pro with Dynamic Island and 48MP camera', 2400000, 'Smartphones', '14pro.jpg', 30, 1],
            ['iPhone 15 Pro', 'Latest iPhone with A17 Pro chip, titanium design, and advanced camera system', 2600000, 'Smartphones', '15pro.jpg', 50, 1],
            ['MacBook Air M2', 'MacBook Air with M2 chip, 13.6-inch Liquid Retina display', 1199000, 'Laptops', 'macbook-air-m2.jpg', 20, 0],
            ['iPad Pro 12.9"', 'iPad Pro with M2 chip and Liquid Retina XDR display', 1099000, 'Tablets', 'ipad-pro-12.jpg', 15, 0],
            ['AirPods Pro 2nd Gen', 'AirPods Pro with Active Noise Cancellation and Spatial Audio', 249000, 'Audio', 'airpods-pro-2.jpg', 100, 0],
            ['Apple Watch Series 9', 'Apple Watch Series 9 with S9 chip and Double Tap gesture', 399000, 'Wearables', 'apple-watch-9.jpg', 40, 0],
            ['Samsung Galaxy S24 Ultra', 'Samsung Galaxy S24 Ultra with S Pen and AI features', 1199000, 'Smartphones', 'galaxy-s24-ultra.jpg', 35, 0]
        ];

        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category, image, stock_quantity, featured) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sample_products as $product) {
            $stmt->execute($product);
        }
        echo "<p>✓ Sample products inserted</p>";
    }

    echo "<h3>Database setup completed successfully!</h3>";
    echo "<p><a href='index.php'>Go to Homepage</a></p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
