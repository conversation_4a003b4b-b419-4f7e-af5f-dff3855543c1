<?php
require_once '../includes/functions.php';

// Require customer login
require_role('customer');

$user = get_logged_in_user();

// Get recent orders
global $pdo;
$stmt = $pdo->prepare("
    SELECT o.*, COUNT(oi.id) as item_count 
    FROM orders o 
    LEFT JOIN order_items oi ON o.id = oi.order_id 
    WHERE o.user_id = ? 
    GROUP BY o.id 
    ORDER BY o.created_at DESC 
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$recent_orders = $stmt->fetchAll();

// Get cart items count
$cart_count = count(get_cart_items());

$flash = get_flash_message();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Account - Griffin Gadgets</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.php" class="logo">
                    <i class="fas fa-bolt"></i> Griffin Gadgets
                </a>
                
                <ul class="nav-links">
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="../products.php">Products</a></li>
                    <li><a href="index.php" class="active">My Account</a></li>
                </ul>
                
                <div class="user-menu">
                    <a href="../cart.php" class="btn btn-info">
                        <i class="fas fa-shopping-cart"></i> Cart (<?= $cart_count ?>)
                    </a>
                    <span>Welcome, <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></span>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-2">
            <div class="alert alert-<?= $flash['type'] ?>">
                <?= htmlspecialchars($flash['message']) ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="container mt-3">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-3">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-user"></i> My Account</h3>
                    </div>
                    <div class="card-body">
                        <nav class="account-nav">
                            <a href="index.php" class="nav-item active">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                            <a href="profile.php" class="nav-item">
                                <i class="fas fa-user-edit"></i> Profile
                            </a>
                            <a href="orders.php" class="nav-item">
                                <i class="fas fa-shopping-bag"></i> My Orders
                            </a>
                            <a href="../cart.php" class="nav-item">
                                <i class="fas fa-shopping-cart"></i> Shopping Cart
                            </a>
                            <a href="change-password.php" class="nav-item">
                                <i class="fas fa-key"></i> Change Password
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-9">
                <!-- Welcome Card -->
                <div class="card mb-3">
                    <div class="card-body">
                        <h2>Welcome back, <?= htmlspecialchars($user['full_name'] ?: $user['username']) ?>!</h2>
                        <p class="text-muted">Manage your account, view orders, and shop for the latest gadgets.</p>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-3">
                    <div class="col-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="feature-icon shipping mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <h3><?= count($recent_orders) ?></h3>
                                <p>Total Orders</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="feature-icon support mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <h3><?= $cart_count ?></h3>
                                <p>Items in Cart</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="feature-icon warranty mb-2" style="width: 60px; height: 60px; margin: 0 auto;">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <h3><?= ucfirst($user['status']) ?></h3>
                                <p>Account Status</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Recent Orders</h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_orders)): ?>
                            <div class="text-center p-3">
                                <i class="fas fa-shopping-bag" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                                <h4>No orders yet</h4>
                                <p class="text-muted">Start shopping to see your orders here!</p>
                                <a href="../products.php" class="btn btn-primary">Browse Products</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Date</th>
                                            <th>Items</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($order['order_number']) ?></td>
                                                <td><?= date('M j, Y', strtotime($order['created_at'])) ?></td>
                                                <td><?= $order['item_count'] ?> items</td>
                                                <td><?= format_currency($order['total_amount']) ?></td>
                                                <td>
                                                    <span class="badge badge-<?= $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'cancelled' ? 'danger' : 'warning') ?>">
                                                        <?= ucfirst($order['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="order-details.php?id=<?= $order['id'] ?>" class="btn btn-sm btn-info">
                                                        View Details
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="orders.php" class="btn btn-primary">View All Orders</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mt-3">
                    <div class="col-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart" style="font-size: 2rem; color: #667eea; margin-bottom: 1rem;"></i>
                                <h4>Continue Shopping</h4>
                                <p>Discover our latest products and exclusive deals</p>
                                <a href="../products.php" class="btn btn-primary">Browse Products</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-edit" style="font-size: 2rem; color: #28a745; margin-bottom: 1rem;"></i>
                                <h4>Update Profile</h4>
                                <p>Keep your account information up to date</p>
                                <a href="profile.php" class="btn btn-success">Edit Profile</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <style>
        .account-nav {
            display: flex;
            flex-direction: column;
        }
        .nav-item {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        .nav-item:hover,
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .badge-success { background: #28a745; color: white; }
        .badge-warning { background: #ffc107; color: #212529; }
        .badge-danger { background: #dc3545; color: white; }
    </style>
</body>
</html>
